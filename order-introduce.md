# Order System Entity Relations Documentation

This document describes the order system entity relationships for H5 page development in Taro project, covering both **Order List Page** and **Order Detail Page**.

## Overview

The order system follows a hierarchical structure with multiple levels of tracking and management:

```
Order (Root Entity)
├── Order Lines (Size-based tracking)
├── Order Crafts (Workflow processes)
│   └── Order Craft Routes (Skill-level operations)
│       └── Order Craft Route Instances (Worker completions)
└── Order Parts (Physical components)
    └── Order Bundles (Production units)
```

## Core Entities and Relationships

### 1. Order (Root Entity)

**Purpose:** Main order record representing a production job

**Key Fields for H5 Display:**
```json
{
  "id": "number - Primary key",
  "order_no": "string - Display order number",
  "external_order_no": "string - Client order reference",
  "skc_no": "string - Style/SKU number",
  "factory_id": "number - Manufacturing facility",
  "owner_user_id": "number - Responsible person",
  "status": "enum - PENDING|IN_PROGRESS|COMPLETED|CANCELLED|ON_HOLD|DELAYED",
  "total_amount": "number - Total quantity across all sizes",
  "cost": "decimal - Production cost",
  "price": "decimal - Order value",
  "current_craft": "string - Current processing stage",
  "created_at": "datetime - Order creation time",
  "started_at": "datetime - Production start time",
  "expect_finished_at": "datetime - Expected completion",
  "finished_at": "datetime - Actual completion time"
}
```

**Status Color Mapping for UI:**
- `PENDING`: #FFA500 (Orange)
- `IN_PROGRESS`: #1890FF (Blue)
- `COMPLETED`: #52C41A (Green)
- `CANCELLED`: #FF4D4F (Red)
- `ON_HOLD`: #FAAD14 (Yellow)
- `DELAYED`: #F5222D (Dark Red)

### 2. Order Lines (1:M relationship with Order)

**Purpose:** Size-based quantity tracking for each order

**Key Fields for H5 Display:**
```json
{
  "id": "number - Primary key",
  "order_id": "number - References Order.id",
  "order_line_no": "string - Format: {order_no}_{size}",
  "size": "string - Product size (S, M, L, XL, etc.)",
  "amount": "number - Total quantity for this size",
  "produced_amount": "number - Quantity produced",
  "completed_amount": "number - Quantity completed",
  "current_craft_route_code": "string - Current processing stage"
}
```

**Calculated Fields for UI:**
- Production Progress: `(produced_amount / amount) * 100`
- Completion Progress: `(completed_amount / amount) * 100`
- Remaining Quantity: `amount - completed_amount`

### 3. Order Crafts (1:M relationship with Order)

**Purpose:** Manufacturing process workflow assigned to orders

**Key Fields for H5 Display:**
```json
{
  "id": "number - Primary key",
  "order_no": "string - References Order.order_no",
  "craft_code": "string - Links to Craft entity",
  "order": "number - Sequence in workflow",
  "status": "enum - pending|in_progress|completed|skipped",
  "is_required": "boolean - Whether craft is mandatory",
  "is_active": "boolean - Whether craft is currently active",
  "estimated_duration_hours": "number - Planned time",
  "actual_duration_hours": "number - Actual time spent",
  "start_time": "datetime - When craft started",
  "end_time": "datetime - When craft completed"
}
```

**Status Color Mapping for UI:**
- `pending`: #D9D9D9 (Gray)
- `in_progress`: #1890FF (Blue)
- `completed`: #52C41A (Green)
- `skipped`: #FAAD14 (Yellow)

### 4. Order Craft Routes (1:M relationship with Order Craft)

**Purpose:** Detailed skill-level operations within each craft

**Key Fields for H5 Display:**
```json
{
  "id": "number - Primary key",
  "order_craft_id": "number - References OrderCraft.id",
  "skill_code": "string - Links to Skill entity",
  "order": "number - Sequence within craft",
  "assigned_user_id": "number - Worker assignment",
  "price": "decimal - Operation price",
  "total_cost": "decimal - Total cost",
  "quality_score": "number - Quality rating (0-100)",
  "measurement_types": "array - Measurement categories",
  "registration_types": "array - Registration categories",
  "measurement_data": "json - Measurement results",
  "registration_data": "json - Registration records"
}
```

### 5. Order Parts (1:M relationship with Order)

**Purpose:** Physical garment components

**Key Fields for H5 Display:**
```json
{
  "id": "number - Primary key",
  "order_id": "number - References Order.id",
  "part_type": "enum - FRONT_BODY|BACK_BODY|SLEEVE|COLLAR|CUFF|POCKET",
  "part_sequence": "number - Order within garment",
  "status": "enum - PLANNED|CUTTING|SEWING|QUALITY_CHECK|COMPLETED",
  "machine_id": "number - Assigned machine",
  "process_notes": "string - Processing instructions"
}
```

### 6. Order Bundles (1:M relationship with Order Part)

**Purpose:** Size-specific production units

**Key Fields for H5 Display:**
```json
{
  "id": "number - Primary key",
  "order_part_id": "number - References OrderPart.id",
  "size": "string - Bundle size",
  "quantity": "number - Bundle quantity",
  "cutting_status": "enum - Status levels",
  "sewing_status": "enum - Status levels",
  "qc_status": "enum - Status levels",
  "assigned_cutter_id": "number - Cutting worker",
  "assigned_sewer_id": "number - Sewing worker",
  "assigned_qc_id": "number - QC worker"
}
```

## H5 Page Specifications

### Order List Page

**Display Requirements:**

1. **Card-based Layout** with key information:
   ```javascript
   {
     orderNo: "Order display number",
     skcNo: "Style/SKU",
     status: "Current status with color coding",
     totalAmount: "Total quantity",
     completionRate: "Overall completion percentage",
     currentCraft: "Current processing stage",
     expectedFinish: "Expected completion date",
     createdAt: "Order creation date"
   }
   ```

2. **Filter Options:**
   - Status filter (multi-select)
   - Date range filter
   - Factory filter
   - Owner filter
   - Search by order number or SKU

3. **Sort Options:**
   - Creation date (newest/oldest)
   - Expected finish date
   - Completion rate
   - Order number

### Order Detail Page

**Section Structure:**

1. **Order Summary Section:**
   - Basic order information
   - Status timeline
   - Progress indicators
   - Key metrics (completion rate, time tracking)

2. **Size Breakdown Section (Order Lines):**
   - Table showing size-wise quantities
   - Progress bars for each size
   - Current processing stage per size

3. **Workflow Progress Section (Order Crafts):**
   - Timeline view of craft processes
   - Status indicators for each craft
   - Time estimates vs actual time
   - Worker assignments

4. **Detailed Operations Section (Order Craft Routes):**
   - Expandable craft sections
   - Skill-level operation details
   - Quality scores
   - Measurement and registration data

5. **Physical Production Section (Order Parts & Bundles):**
   - Garment component breakdown
   - Bundle-level tracking
   - Machine assignments
   - Worker assignments per stage

## API Endpoints for H5 Integration

### Order List Page APIs:
```
GET /api/v1/orders/
  - Query params: status, factory_id, date_from, date_to, search
  - Returns: Paginated order list with summary data

GET /api/v1/orders/filters/
  - Returns: Available filter options (factories, users, statuses)
```

### Order Detail Page APIs:
```
GET /api/v1/orders/{id}/
  - Returns: Complete order details with all relationships

GET /api/v1/orders/{id}/lines/
  - Returns: Order lines with progress data

GET /api/v1/orders/{id}/crafts/
  - Returns: Order crafts with routes and instances

GET /api/v1/orders/{id}/parts/
  - Returns: Order parts with bundles

GET /api/v1/orders/{id}/progress/
  - Returns: Real-time progress summary
```

## Data Relationships Summary

**For Order List Page:**
- Primary focus on Order entity with calculated completion rates
- Minimal related data loading for performance

**For Order Detail Page:**
- Full relationship loading:
  - Order → Order Lines (size tracking)
  - Order → Order Crafts → Order Craft Routes (workflow)
  - Order → Order Parts → Order Bundles (physical production)
  - Related entities: Users, Crafts, Skills, Departments

## UI Component Recommendations

1. **Progress Indicators:** Use progress bars with percentage labels
2. **Status Badges:** Color-coded status indicators
3. **Timeline Components:** For workflow visualization
4. **Expandable Sections:** For detailed craft route information
5. **Data Tables:** For size breakdowns and bundle tracking
6. **Charts:** For progress visualization and time tracking

## Performance Considerations

1. **List Page:** Implement pagination and lazy loading
2. **Detail Page:** Use tabs or accordion for large datasets
3. **Real-time Updates:** Consider WebSocket for live progress updates
4. **Caching:** Cache frequently accessed data like crafts and skills
5. **Offline Support:** Store critical order data for offline viewing

This documentation provides the complete structure needed to implement both order list and detail pages in your Taro H5 application, with full understanding of entity relationships and data flow.
