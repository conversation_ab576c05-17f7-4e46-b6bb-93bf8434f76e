// Order utility functions

export interface OrderStatus {
  value: string
  label: string
  color: string
}

// Status configurations
export const ORDER_STATUSES: OrderStatus[] = [
  { value: 'PENDING', label: '待处理', color: '#FFA500' },
  { value: 'IN_PROGRESS', label: '进行中', color: '#1890FF' },
  { value: 'COMPLETED', label: '已完成', color: '#52C41A' },
  { value: 'CANCELLED', label: '已取消', color: '#FF4D4F' },
  { value: 'ON_HOLD', label: '暂停', color: '#FAAD14' },
  { value: 'DELAYED', label: '延期', color: '#F5222D' }
]

export const CRAFT_STATUSES: OrderStatus[] = [
  { value: 'pending', label: '待开始', color: '#D9D9D9' },
  { value: 'in_progress', label: '进行中', color: '#1890FF' },
  { value: 'completed', label: '已完成', color: '#52C41A' },
  { value: 'skipped', label: '已跳过', color: '#FAAD14' }
]

// Get status configuration
export const getStatusConfig = (status: string, type: 'order' | 'craft' = 'order'): OrderStatus => {
  const statuses = type === 'order' ? ORDER_STATUSES : CRAFT_STATUSES
  return statuses.find(s => s.value === status) || { value: status, label: status, color: '#999999' }
}

// Format date utilities
export const formatDate = (dateString: string | null, format: 'date' | 'datetime' | 'time' = 'date'): string => {
  if (!dateString) return '-'
  
  const date = new Date(dateString)
  
  if (isNaN(date.getTime())) return '-'
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  switch (format) {
    case 'date':
      return `${year}-${month}-${day}`
    case 'time':
      return `${hours}:${minutes}`
    case 'datetime':
      return `${year}-${month}-${day} ${hours}:${minutes}`
    default:
      return `${year}-${month}-${day}`
  }
}

// Format relative time (e.g., "2 days ago", "in 3 hours")
export const formatRelativeTime = (dateString: string | null): string => {
  if (!dateString) return '-'
  
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = date.getTime() - now.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  
  if (Math.abs(diffDays) >= 1) {
    return diffDays > 0 ? `${diffDays}天后` : `${Math.abs(diffDays)}天前`
  } else if (Math.abs(diffHours) >= 1) {
    return diffHours > 0 ? `${diffHours}小时后` : `${Math.abs(diffHours)}小时前`
  } else if (Math.abs(diffMinutes) >= 1) {
    return diffMinutes > 0 ? `${diffMinutes}分钟后` : `${Math.abs(diffMinutes)}分钟前`
  } else {
    return '刚刚'
  }
}

// Calculate progress percentage
export const calculateProgress = (completed: number, total: number): number => {
  if (total === 0) return 0
  return Math.round((completed / total) * 100 * 10) / 10 // Round to 1 decimal place
}

// Format currency
export const formatCurrency = (amount: number): string => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// Format number with commas
export const formatNumber = (num: number): string => {
  return num.toLocaleString('zh-CN')
}

// Get progress color based on percentage
export const getProgressColor = (percentage: number): string => {
  if (percentage >= 90) return '#52C41A' // Green
  if (percentage >= 70) return '#1890FF' // Blue
  if (percentage >= 50) return '#FAAD14' // Yellow
  if (percentage >= 30) return '#FA8C16' // Orange
  return '#FF4D4F' // Red
}

// Sort orders by different criteria
export const sortOrders = (orders: any[], sortBy: string, sortOrder: 'asc' | 'desc' = 'desc') => {
  return [...orders].sort((a, b) => {
    let aValue: any
    let bValue: any
    
    switch (sortBy) {
      case 'created_at':
      case 'expect_finished_at':
      case 'started_at':
      case 'finished_at':
        aValue = a[sortBy] ? new Date(a[sortBy]).getTime() : 0
        bValue = b[sortBy] ? new Date(b[sortBy]).getTime() : 0
        break
      case 'completion_rate':
      case 'total_amount':
      case 'cost':
      case 'price':
        aValue = a[sortBy] || 0
        bValue = b[sortBy] || 0
        break
      case 'order_no':
      case 'skc_no':
      case 'status':
        aValue = a[sortBy] || ''
        bValue = b[sortBy] || ''
        break
      default:
        aValue = a[sortBy] || ''
        bValue = b[sortBy] || ''
    }
    
    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    } else {
      return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
    }
  })
}

// Filter orders based on criteria
export const filterOrders = (orders: any[], filters: {
  status?: string[]
  search?: string
  dateFrom?: string
  dateTo?: string
}) => {
  return orders.filter(order => {
    // Status filter
    if (filters.status && filters.status.length > 0) {
      if (!filters.status.includes(order.status)) {
        return false
      }
    }
    
    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase()
      const searchableFields = [
        order.order_no,
        order.external_order_no,
        order.skc_no,
        order.current_craft
      ].filter(Boolean).map(field => field.toLowerCase())
      
      if (!searchableFields.some(field => field.includes(searchTerm))) {
        return false
      }
    }
    
    // Date range filter
    if (filters.dateFrom || filters.dateTo) {
      const orderDate = new Date(order.created_at)
      
      if (filters.dateFrom) {
        const fromDate = new Date(filters.dateFrom)
        if (orderDate < fromDate) return false
      }
      
      if (filters.dateTo) {
        const toDate = new Date(filters.dateTo)
        toDate.setHours(23, 59, 59, 999) // End of day
        if (orderDate > toDate) return false
      }
    }
    
    return true
  })
}
