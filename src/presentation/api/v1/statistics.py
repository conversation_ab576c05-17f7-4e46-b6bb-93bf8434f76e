from typing import Optional
from datetime import date
from fastapi import APIRouter, Depends, HTTPException, status, Query
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.statistics_use_cases import StatisticsUseCases
from src.application.dto.statistics_dto import (
    DashboardStatisticsDTO, StatisticsSearchDTO
)
from src.presentation.api.v1.auth import get_current_active_user
from src.presentation.api.v1.factory_management import get_current_factory_from_session
from src.domain.entities.user import User

router = APIRouter(prefix="/statistics", tags=["statistics"])
logger = get_logger(__name__)


@router.get("/dashboard", response_model=DashboardStatisticsDTO)
@inject
async def get_dashboard_statistics(
    target_date: Optional[date] = Query(None, description="目标日期，默认为今天"),
    include_details: bool = Query(True, description="是否包含详细信息"),
    max_recent_records: int = Query(10, ge=1, le=50, description="最大最近记录数"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    statistics_use_cases: StatisticsUseCases = Depends(Provide[Container.statistics_use_cases])
):
    """Get comprehensive dashboard statistics for the current factory."""
    logger.info(
        "Getting dashboard statistics",
        user_id=current_user.id,
        factory_id=current_factory_id,
        target_date=target_date,
        include_details=include_details
    )
    
    try:
        # Check if user has permission to view statistics
        if not current_user.has_any_permission(["statistics.view", "dashboard.view", "orders.view"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view statistics"
            )
        
        # Create search parameters
        search_params = StatisticsSearchDTO(
            date=target_date,
            include_details=include_details,
            max_recent_records=max_recent_records
        )
        
        # Get dashboard statistics
        dashboard_stats = await statistics_use_cases.get_dashboard_statistics(
            factory_id=current_factory_id,
            search_params=search_params
        )
        
        logger.info(
            "Successfully retrieved dashboard statistics",
            user_id=current_user.id,
            factory_id=current_factory_id,
            scan_count=dashboard_stats.today_scan_count.current_value,
            online_employees=dashboard_stats.online_employees.current_value,
            production_value=float(dashboard_stats.today_production_value.current_value),
            completed_orders=dashboard_stats.completed_orders.current_value
        )
        
        return dashboard_stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error retrieving dashboard statistics: {e}",
            user_id=current_user.id,
            factory_id=current_factory_id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard statistics"
        )


@router.get("/scan-summary")
@inject
async def get_scan_summary(
    target_date: Optional[date] = Query(None, description="目标日期，默认为今天"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    statistics_use_cases: StatisticsUseCases = Depends(Provide[Container.statistics_use_cases])
):
    """Get scan count summary for the specified date."""
    logger.info(
        "Getting scan summary",
        user_id=current_user.id,
        factory_id=current_factory_id,
        target_date=target_date
    )
    
    try:
        # Check if user has permission to view statistics
        if not current_user.has_any_permission(["statistics.view", "dashboard.view", "craft_instances.view"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view scan statistics"
            )
        
        # Get dashboard statistics (we only need scan data)
        search_params = StatisticsSearchDTO(
            date=target_date,
            include_details=False,
            max_recent_records=0
        )
        
        dashboard_stats = await statistics_use_cases.get_dashboard_statistics(
            factory_id=current_factory_id,
            search_params=search_params
        )
        
        scan_summary = {
            "date": dashboard_stats.statistics_date,
            "factory_id": dashboard_stats.factory_id,
            "factory_name": dashboard_stats.factory_name,
            "scan_count": dashboard_stats.today_scan_count.current_value,
            "previous_scan_count": dashboard_stats.today_scan_count.previous_value,
            "change_percentage": dashboard_stats.today_scan_count.change_percentage,
            "change_absolute": dashboard_stats.today_scan_count.change_absolute,
            "change_text": dashboard_stats.today_scan_count.change_text,
            "change_percentage_text": dashboard_stats.today_scan_count.change_percentage_text
        }
        
        logger.info(
            "Successfully retrieved scan summary",
            user_id=current_user.id,
            factory_id=current_factory_id,
            scan_count=scan_summary["scan_count"]
        )
        
        return scan_summary
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error retrieving scan summary: {e}",
            user_id=current_user.id,
            factory_id=current_factory_id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve scan summary"
        )


@router.get("/online-employees")
@inject
async def get_online_employees(
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    statistics_use_cases: StatisticsUseCases = Depends(Provide[Container.statistics_use_cases])
):
    """Get online employees information."""
    logger.info(
        "Getting online employees",
        user_id=current_user.id,
        factory_id=current_factory_id
    )
    
    try:
        # Check if user has permission to view employee information
        if not current_user.has_any_permission(["statistics.view", "users.view", "factory.view_members"]):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view employee information"
            )
        
        # Get dashboard statistics (we only need online employee data)
        search_params = StatisticsSearchDTO(
            include_details=True,
            max_recent_records=0
        )
        
        dashboard_stats = await statistics_use_cases.get_dashboard_statistics(
            factory_id=current_factory_id,
            search_params=search_params
        )
        
        online_employees_info = {
            "factory_id": dashboard_stats.factory_id,
            "factory_name": dashboard_stats.factory_name,
            "online_count": dashboard_stats.online_employees.current_value,
            "previous_online_count": dashboard_stats.online_employees.previous_value,
            "change_percentage": dashboard_stats.online_employees.change_percentage,
            "change_absolute": dashboard_stats.online_employees.change_absolute,
            "change_text": dashboard_stats.online_employees.change_text,
            "change_percentage_text": dashboard_stats.online_employees.change_percentage_text,
            "online_employee_details": dashboard_stats.online_employee_details,
            "generated_at": dashboard_stats.generated_at
        }
        
        logger.info(
            "Successfully retrieved online employees information",
            user_id=current_user.id,
            factory_id=current_factory_id,
            online_count=online_employees_info["online_count"]
        )
        
        return online_employees_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error retrieving online employees information: {e}",
            user_id=current_user.id,
            factory_id=current_factory_id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve online employees information"
        )
