from typing import List, Optional, Dict, Any
from datetime import datetime, date
from pydantic import BaseModel, Field
from decimal import Decimal


class DailyMetricDTO(BaseModel):
    """DTO for daily metric with comparison to previous day."""
    current_value: int = Field(..., description="当前值")
    previous_value: int = Field(..., description="前一天值")
    change_percentage: float = Field(..., description="变化百分比")
    change_absolute: int = Field(..., description="绝对变化量")
    
    @property
    def change_text(self) -> str:
        """Get formatted change text."""
        if self.change_absolute > 0:
            return f"+{self.change_absolute}"
        elif self.change_absolute < 0:
            return str(self.change_absolute)
        else:
            return "0"
    
    @property
    def change_percentage_text(self) -> str:
        """Get formatted percentage change text."""
        if self.change_percentage > 0:
            return f"+{self.change_percentage:.1f}%"
        elif self.change_percentage < 0:
            return f"{self.change_percentage:.1f}%"
        else:
            return "0%"


class ProductionValueDTO(BaseModel):
    """DTO for production value metric."""
    current_value: Decimal = Field(..., description="当前产值")
    previous_value: Decimal = Field(..., description="前一天产值")
    change_percentage: float = Field(..., description="变化百分比")
    change_absolute: Decimal = Field(..., description="绝对变化量")
    currency_symbol: str = Field(default="¥", description="货币符号")
    
    @property
    def formatted_current_value(self) -> str:
        """Get formatted current value."""
        return f"{self.currency_symbol}{self.current_value:,.0f}"
    
    @property
    def change_text(self) -> str:
        """Get formatted change text."""
        if self.change_absolute > 0:
            return f"+{self.change_absolute:.0f}"
        elif self.change_absolute < 0:
            return f"{self.change_absolute:.0f}"
        else:
            return "0"
    
    @property
    def change_percentage_text(self) -> str:
        """Get formatted percentage change text."""
        if self.change_percentage > 0:
            return f"+{self.change_percentage:.1f}%"
        elif self.change_percentage < 0:
            return f"{self.change_percentage:.1f}%"
        else:
            return "0%"


class RecentScanRecordDTO(BaseModel):
    """DTO for recent scan record."""
    worker_name: str = Field(..., description="工人姓名")
    operation_description: str = Field(..., description="操作描述")
    scan_time: datetime = Field(..., description="扫码时间")
    time_ago_text: str = Field(..., description="时间前文本")
    status_color: str = Field(default="green", description="状态颜色")
    
    class Config:
        from_attributes = True


class OnlineEmployeeDTO(BaseModel):
    """DTO for online employee information."""
    user_id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    full_name: Optional[str] = Field(None, description="全名")
    department_name: Optional[str] = Field(None, description="部门名称")
    role: Optional[str] = Field(None, description="角色")
    last_activity: datetime = Field(..., description="最后活动时间")
    session_duration_minutes: int = Field(..., description="会话持续时间(分钟)")
    
    class Config:
        from_attributes = True


class DashboardStatisticsDTO(BaseModel):
    """DTO for comprehensive dashboard statistics."""
    # 主要指标
    today_scan_count: DailyMetricDTO = Field(..., description="今日扫码数")
    online_employees: DailyMetricDTO = Field(..., description="在线员工")
    today_production_value: ProductionValueDTO = Field(..., description="今日产值")
    completed_orders: DailyMetricDTO = Field(..., description="完成订单")
    
    # 最近扫码记录
    recent_scan_records: List[RecentScanRecordDTO] = Field(..., description="最近扫码记录")
    
    # 在线员工详情
    online_employee_details: List[OnlineEmployeeDTO] = Field(..., description="在线员工详情")
    
    # 统计时间
    statistics_date: date = Field(..., description="统计日期")
    generated_at: datetime = Field(..., description="生成时间")
    factory_id: int = Field(..., description="工厂ID")
    factory_name: Optional[str] = Field(None, description="工厂名称")


class StatisticsSearchDTO:
    """DTO for statistics search parameters."""
    def __init__(self, date: Optional[date] = None, include_details: bool = True, max_recent_records: int = 10):
        self.date = date
        self.include_details = include_details
        self.max_recent_records = max_recent_records
