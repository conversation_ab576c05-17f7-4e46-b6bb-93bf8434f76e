from typing import List, Optional
from pydantic import BaseModel, Field


class AvailableBundleDTO(BaseModel):
    """可登记的订单扎信息"""
    order_bundle_no: str = Field(..., description="订单扎号")
    total_quantity: int = Field(..., description="扎总数量")
    registered_quantity: int = Field(default=0, description="已登记数量")
    available_quantity: int = Field(..., description="可登记数量")
    size: Optional[str] = Field(None, description="扎尺寸")
    bundle_sequence: Optional[int] = Field(None, description="扎序号")


class AvailablePartDTO(BaseModel):
    """可登记的订单部位信息"""
    order_part_no: str = Field(..., description="订单部位号")
    total_quantity: int = Field(..., description="部位总数量")
    registered_quantity: int = Field(default=0, description="已登记数量")
    available_quantity: int = Field(..., description="可登记数量")
    order_part_name: Optional[str] = Field(None, description="订单部位名称")
    order_bundles: List[AvailableBundleDTO] = Field(default_factory=list, description="该部位下的可登记扎列表")


class AvailableCraftRouteDTO(BaseModel):
    """可登记的工艺路线信息"""
    order_craft_route_id: int = Field(..., description="订单工艺路线ID")
    craft_route_name: str = Field(..., description="工艺路线名称")
    skill_code: str = Field(..., description="技能代码")
    skill_name: Optional[str] = Field(None, description="技能名称")
    route_status: str = Field(..., description="路线状态")
    total_quantity: int = Field(..., description="路线总数量")
    registered_quantity: int = Field(default=0, description="已登记数量")
    available_quantity: int = Field(..., description="可登记数量")
    
    # 支持的登记粒度
    supports_order_level: bool = Field(default=True, description="支持整单级别登记")
    supports_part_level: bool = Field(default=True, description="支持部位级别登记")
    supports_bundle_level: bool = Field(default=True, description="支持扎级别登记")


class AvailableRegistrationDTO(BaseModel):
    """订单可登记数据"""
    order_no: str = Field(..., description="订单号")
    order_status: str = Field(..., description="订单状态")
    total_quantity: int = Field(..., description="订单总数量")
    registered_quantity: int = Field(default=0, description="已登记数量")
    available_quantity: int = Field(..., description="可登记数量")
    
    # 可登记的工艺路线列表
    craft_routes: List[AvailableCraftRouteDTO] = Field(default_factory=list, description="可登记的工艺路线列表")
    
    # 可登记的部位和扎结构
    order_parts: List[AvailablePartDTO] = Field(default_factory=list, description="可登记的订单部位列表")


class AvailableRegistrationQueryDTO(BaseModel):
    """查询可登记数据的参数"""
    order_no: str = Field(..., description="订单号")
    craft_route_id: Optional[int] = Field(None, description="指定工艺路线ID (可选)")
    include_completed_routes: bool = Field(default=False, description="是否包含已完成的路线")
    granularity_filter: Optional[str] = Field(None, description="粒度过滤: order/part/bundle")


class RegistrationSummaryDTO(BaseModel):
    """登记汇总信息"""
    total_craft_routes: int = Field(..., description="总工艺路线数")
    available_craft_routes: int = Field(..., description="可登记工艺路线数")
    total_parts: int = Field(..., description="总部位数")
    available_parts: int = Field(..., description="可登记部位数")
    total_bundles: int = Field(..., description="总扎数")
    available_bundles: int = Field(..., description="可登记扎数")
    completion_rate: float = Field(..., description="完成率 (0-1)")