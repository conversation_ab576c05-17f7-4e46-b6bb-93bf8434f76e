from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from decimal import Decimal

from src.infrastructure.logging.logger import get_logger
from src.infrastructure.repositories.statistics_repository import StatisticsRepository
from src.infrastructure.external_services.session_service import SessionServiceInterface
from src.application.dto.statistics_dto import (
    DashboardStatisticsDTO, DailyMetricDTO, ProductionValueDTO, 
    RecentScanRecordDTO, OnlineEmployeeDTO, StatisticsSearchDTO
)

logger = get_logger(__name__)


class StatisticsUseCases:
    """Use cases for statistics operations."""
    
    def __init__(
        self, 
        statistics_repository: StatisticsRepository,
        session_service: SessionServiceInterface
    ):
        self.statistics_repository = statistics_repository
        self.session_service = session_service
    
    def _calculate_percentage_change(self, current: float, previous: float) -> float:
        """Calculate percentage change between current and previous values."""
        if previous == 0:
            return 100.0 if current > 0 else 0.0
        return ((current - previous) / previous) * 100.0
    
    async def _get_online_employees_count(self, factory_id: int) -> Dict[str, int]:
        """Get count of online employees for current and previous day."""
        try:
            # Get all active sessions from Redis
            # This is a simplified approach - in a real implementation, you might want to
            # track session activity more precisely
            
            # For now, we'll simulate online employee count based on factory members
            # In a real implementation, you would check active sessions in Redis
            factory_members = await self.statistics_repository.get_active_factory_members(factory_id)
            
            # Simulate current online count (in real implementation, check active sessions)
            current_online = min(len(factory_members), max(1, len(factory_members) // 3))
            
            # Simulate previous day online count
            previous_online = max(1, current_online - 3)
            
            return {
                "current": current_online,
                "previous": previous_online
            }
        except Exception as e:
            logger.error(f"Error getting online employees count: {e}")
            return {"current": 0, "previous": 0}
    
    async def _get_online_employee_details(self, factory_id: int) -> List[OnlineEmployeeDTO]:
        """Get detailed information about online employees."""
        try:
            # Get factory members
            factory_members = await self.statistics_repository.get_active_factory_members(factory_id)
            
            # In a real implementation, you would check which users have active sessions
            # For now, we'll simulate some online employees
            online_employees = []
            current_time = datetime.utcnow()
            
            # Simulate some online employees (take first few members)
            for i, member in enumerate(factory_members[:min(len(factory_members), 5)]):
                # Simulate session duration
                session_start = current_time - timedelta(minutes=30 + i * 15)
                duration_minutes = int((current_time - session_start).total_seconds() / 60)
                
                online_employees.append(OnlineEmployeeDTO(
                    user_id=member["user_id"],
                    username=member["username"],
                    full_name=member["full_name"],
                    department_name=member["department_name"],
                    role=member["role"],
                    last_activity=current_time - timedelta(minutes=i * 2),
                    session_duration_minutes=duration_minutes
                ))
            
            return online_employees
        except Exception as e:
            logger.error(f"Error getting online employee details: {e}")
            return []
    
    async def get_dashboard_statistics(
        self, 
        factory_id: int, 
        search_params: Optional[StatisticsSearchDTO] = None
    ) -> DashboardStatisticsDTO:
        """Get comprehensive dashboard statistics."""
        logger.info(f"Getting dashboard statistics for factory {factory_id}")
        
        if search_params is None:
            search_params = StatisticsSearchDTO()
        
        target_date = search_params.date or date.today()
        
        try:
            # Get factory information
            factory_info = await self.statistics_repository.get_factory_info(factory_id)
            factory_name = factory_info["name"] if factory_info else None
            
            # Get scan count statistics
            scan_stats = await self.statistics_repository.get_daily_scan_count(factory_id, target_date)
            scan_change = scan_stats["current"] - scan_stats["previous"]
            scan_percentage = self._calculate_percentage_change(
                scan_stats["current"], scan_stats["previous"]
            )
            
            today_scan_count = DailyMetricDTO(
                current_value=scan_stats["current"],
                previous_value=scan_stats["previous"],
                change_percentage=scan_percentage,
                change_absolute=scan_change
            )
            
            # Get online employees statistics
            online_stats = await self._get_online_employees_count(factory_id)
            online_change = online_stats["current"] - online_stats["previous"]
            online_percentage = self._calculate_percentage_change(
                online_stats["current"], online_stats["previous"]
            )
            
            online_employees = DailyMetricDTO(
                current_value=online_stats["current"],
                previous_value=online_stats["previous"],
                change_percentage=online_percentage,
                change_absolute=online_change
            )
            
            # Get production value statistics
            production_stats = await self.statistics_repository.get_daily_production_value(factory_id, target_date)
            production_change = production_stats["current"] - production_stats["previous"]
            production_percentage = self._calculate_percentage_change(
                float(production_stats["current"]), float(production_stats["previous"])
            )
            
            today_production_value = ProductionValueDTO(
                current_value=production_stats["current"],
                previous_value=production_stats["previous"],
                change_percentage=production_percentage,
                change_absolute=production_change
            )
            
            # Get completed orders statistics
            orders_stats = await self.statistics_repository.get_daily_completed_orders(factory_id, target_date)
            orders_change = orders_stats["current"] - orders_stats["previous"]
            orders_percentage = self._calculate_percentage_change(
                orders_stats["current"], orders_stats["previous"]
            )
            
            completed_orders = DailyMetricDTO(
                current_value=orders_stats["current"],
                previous_value=orders_stats["previous"],
                change_percentage=orders_percentage,
                change_absolute=orders_change
            )
            
            # Get recent scan records
            recent_records_data = await self.statistics_repository.get_recent_scan_records(
                factory_id, search_params.max_recent_records
            )
            recent_scan_records = [
                RecentScanRecordDTO(**record) for record in recent_records_data
            ]
            
            # Get online employee details
            online_employee_details = []
            if search_params.include_details:
                online_employee_details = await self._get_online_employee_details(factory_id)
            
            dashboard_stats = DashboardStatisticsDTO(
                today_scan_count=today_scan_count,
                online_employees=online_employees,
                today_production_value=today_production_value,
                completed_orders=completed_orders,
                recent_scan_records=recent_scan_records,
                online_employee_details=online_employee_details,
                statistics_date=target_date,
                generated_at=datetime.utcnow(),
                factory_id=factory_id,
                factory_name=factory_name
            )
            
            logger.info(
                f"Successfully generated dashboard statistics for factory {factory_id}",
                scan_count=scan_stats["current"],
                online_employees=online_stats["current"],
                production_value=float(production_stats["current"]),
                completed_orders=orders_stats["current"]
            )
            
            return dashboard_stats
            
        except Exception as e:
            logger.error(f"Error generating dashboard statistics for factory {factory_id}: {e}")
            raise
