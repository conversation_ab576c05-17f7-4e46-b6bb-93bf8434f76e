from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from dependency_injector.wiring import Provide, inject
from sqlalchemy.ext.asyncio import AsyncSession
from src.application.interfaces.order_craft_route_instance_repository_interface import OrderCraftRouteInstanceRepositoryInterface
from src.application.interfaces.order_craft_route_repository_interface import OrderCraftRouteRepositoryInterface
from src.application.interfaces.user_repository_interface import UserRepositoryInterface
from src.application.services.craft_route_workflow_service import CraftRouteWorkflowService
from src.application.dto.craft_instance_dto import (
    CraftInstanceCreateDTO, CraftInstanceUpdateDTO, CraftInstanceResponseDTO,
    CraftInstanceVerificationDTO, CraftInstanceRejectionDTO, CraftInstanceSearchDTO,
    CraftInstanceListDTO, CraftInstanceOperationResultDTO, CraftInstanceStatisticsDTO,
    CraftInstanceBatchCreateDTO, CraftInstanceQRScanDTO
)
from src.domain.entities.order_craft_route_instance import OrderCraftRouteInstance, CompletionGranularity, SettlementStatus
from src.infrastructure.database.database import Database


class CraftInstanceUseCases:
    """Use cases for craft instance (worker completion record) management."""
    
    @inject
    def __init__(
        self,
        instance_repository: OrderCraftRouteInstanceRepositoryInterface,
        route_repository: OrderCraftRouteRepositoryInterface,
        user_repository: UserRepositoryInterface,
        workflow_service: CraftRouteWorkflowService,
        database: Database = Provide["db"]
    ):
        self.instance_repository = instance_repository
        self.route_repository = route_repository
        self.user_repository = user_repository
        self.workflow_service = workflow_service
        self.database = database
    
    async def create_instance(self, data: CraftInstanceCreateDTO, current_factory_id: int) -> CraftInstanceOperationResultDTO:
        """Create a new craft instance - worker completion record."""
        try:
            # Validate route exists
            route = await self.route_repository.get_by_id(data.order_craft_route_id)
            if not route:
                return CraftInstanceOperationResultDTO(
                    success=False,
                    message=f"Order craft route {data.order_craft_route_id} not found",
                    instance_id=None,
                    details={}
                )
            
            # Validate worker exists
            worker = await self.user_repository.get_by_id(data.worker_user_id)
            if not worker:
                return CraftInstanceOperationResultDTO(
                    success=False,
                    message=f"Worker {data.worker_user_id} not found",
                    instance_id=None,
                    details={}
                )
            
            # Create instance
            instance = OrderCraftRouteInstance(
                factory_id=current_factory_id,
                order_craft_route_id=data.order_craft_route_id,
                completion_granularity=CompletionGranularity(data.completion_granularity.value),
                order_no=data.order_no,
                worker_user_id=data.worker_user_id,
                completed_quantity=data.completed_quantity,
                quality_level=data.quality_level,
                started_at=data.started_at,
                completed_at=datetime.now(timezone.utc),
                qr_code_scanned=data.qr_code_scanned,
                scan_location=data.scan_location,
                device_info=data.device_info,
                measurement_data=data.measurement_data,
                registration_data=data.registration_data,
                notes=data.notes
            )
            
            # Handle multiple parts/bundles
            if data.order_part_nos:
                instance.set_part_nos(data.order_part_nos)
            elif data.order_part_no:
                instance.set_part_nos([data.order_part_no])
                
            if data.order_bundle_nos:
                instance.set_bundle_nos(data.order_bundle_nos)
            elif data.order_bundle_no:
                instance.set_bundle_nos([data.order_bundle_no])
            
            # Create instance and process workflow in same session
            async with self.database.session_factory() as session:
                created_instance = await self.instance_repository.create(instance, session)
                
                # Update craft route status based on completion
                workflow_result = await self.workflow_service.process_completion_instance(
                    created_instance, session
                )
                
                await session.commit()
            
            return CraftInstanceOperationResultDTO(
                success=True,
                message="Craft instance created successfully",
                instance_id=created_instance.id,
                details={
                    "order_no": data.order_no,
                    "completed_quantity": data.completed_quantity,
                    "worker_id": data.worker_user_id,
                    "workflow_result": workflow_result
                }
            )
            
        except Exception as e:
            return CraftInstanceOperationResultDTO(
                success=False,
                message=f"Failed to create craft instance: {str(e)}",
                instance_id=None,
                details={}
            )
    
    async def qr_scan_register(self, data: CraftInstanceQRScanDTO, worker_user_id: int, current_factory_id: int) -> CraftInstanceOperationResultDTO:
        """Register completion via QR code scanning."""
        try:
            # Parse QR code to extract route information
            # QR code format: "route:{route_id}:order:{order_no}:parts:{part1,part2}:bundles:{bundle1,bundle2}"
            # or legacy format: "route:{route_id}:order:{order_no}:part:{part_no}:bundle:{bundle_no}"
            qr_parts = data.qr_code_content.split(":")
            if len(qr_parts) < 4 or qr_parts[0] != "route":
                return CraftInstanceOperationResultDTO(
                    success=False,
                    message="Invalid QR code format",
                    instance_id=None,
                    details={"qr_code": data.qr_code_content}
                )
            
            route_id = int(qr_parts[1])
            order_no = qr_parts[3] if len(qr_parts) > 3 else None
            
            # Parse parts and bundles (support both single and multiple)
            part_nos = []
            bundle_nos = []
            
            for i in range(4, len(qr_parts), 2):
                if i + 1 < len(qr_parts):
                    key = qr_parts[i]
                    value = qr_parts[i + 1]
                    
                    if key == "part":
                        part_nos.append(value)
                    elif key == "parts":
                        part_nos.extend(value.split(","))
                    elif key == "bundle":
                        bundle_nos.append(value)
                    elif key == "bundles":
                        bundle_nos.extend(value.split(","))
            
            # Determine granularity
            if bundle_nos:
                granularity = CompletionGranularity.BUNDLE
            elif part_nos:
                granularity = CompletionGranularity.BED
            else:
                granularity = CompletionGranularity.ORDER
            
            # Create instance DTO
            create_data = CraftInstanceCreateDTO(
                order_craft_route_id=route_id,
                completion_granularity=granularity.value,
                order_no=order_no,
                order_part_nos=part_nos if part_nos else None,
                order_bundle_nos=bundle_nos if bundle_nos else None,
                # Legacy single part/bundle for backward compatibility
                order_part_no=part_nos[0] if part_nos else None,
                order_bundle_no=bundle_nos[0] if bundle_nos else None,
                worker_user_id=worker_user_id,
                completed_quantity=data.completed_quantity,
                quality_level=data.quality_level,
                started_at=datetime.now(timezone.utc),
                qr_code_scanned=data.qr_code_content,
                scan_location=data.scan_location,
                device_info=data.device_info,
                measurement_data=data.measurement_data,
                registration_data=data.registration_data,
                notes=data.notes
            )
            
            return await self.create_instance(create_data, current_factory_id)
            
        except Exception as e:
            return CraftInstanceOperationResultDTO(
                success=False,
                message=f"Failed to register via QR scan: {str(e)}",
                instance_id=None,
                details={"qr_code": data.qr_code_content}
            )
    
    async def get_instance_by_id(self, instance_id: int) -> Optional[CraftInstanceResponseDTO]:
        """Get craft instance by ID."""
        instance = await self.instance_repository.get_by_id(instance_id)
        if not instance:
            return None
        
        return await self._build_instance_dto(instance)
    
    async def search_instances(self, search_data: CraftInstanceSearchDTO, limit: int = 50, offset: int = 0) -> CraftInstanceListDTO:
        """Search craft instances with filters."""
        instances = await self.instance_repository.search_instances(
            order_no=search_data.order_no,
            worker_user_id=search_data.worker_user_id,
            completion_granularity=search_data.completion_granularity.value if search_data.completion_granularity else None,
            status=search_data.status,
            settlement_status=search_data.settlement_status.value if search_data.settlement_status else None,
            quality_level=search_data.quality_level,
            start_date=search_data.start_date,
            end_date=search_data.end_date,
            date_field=search_data.date_field,
            limit=limit,
            offset=offset
        )
        
        total = await self.instance_repository.count_instances(
            order_no=search_data.order_no,
            worker_user_id=search_data.worker_user_id,
            completion_granularity=search_data.completion_granularity.value if search_data.completion_granularity else None,
            status=search_data.status,
            settlement_status=search_data.settlement_status.value if search_data.settlement_status else None,
            quality_level=search_data.quality_level,
            start_date=search_data.start_date,
            end_date=search_data.end_date,
            date_field=search_data.date_field
        )
        
        instance_dtos = []
        total_quantity = 0
        
        for instance in instances:
            dto = await self._build_instance_dto(instance)
            instance_dtos.append(dto)
            total_quantity += instance.completed_quantity
        
        return CraftInstanceListDTO(
            instances=instance_dtos,
            total=total,
            total_quantity=total_quantity
        )
    
    async def verify_instance(self, instance_id: int, verification_data: CraftInstanceVerificationDTO) -> CraftInstanceOperationResultDTO:
        """Verify a craft instance."""
        try:
            instance = await self.instance_repository.get_by_id(instance_id)
            if not instance:
                return CraftInstanceOperationResultDTO(
                    success=False,
                    message="Craft instance not found",
                    instance_id=instance_id,
                    details={}
                )
            
            instance.verify_completion(verification_data.verified_by_user_id, verification_data.notes)
            await self.instance_repository.update(instance)
            
            return CraftInstanceOperationResultDTO(
                success=True,
                message="Craft instance verified successfully",
                instance_id=instance_id,
                details={"status": "verified"}
            )
            
        except Exception as e:
            return CraftInstanceOperationResultDTO(
                success=False,
                message=f"Failed to verify instance: {str(e)}",
                instance_id=instance_id,
                details={}
            )
    
    async def reject_instance(self, instance_id: int, rejection_data: CraftInstanceRejectionDTO) -> CraftInstanceOperationResultDTO:
        """Reject a craft instance."""
        try:
            instance = await self.instance_repository.get_by_id(instance_id)
            if not instance:
                return CraftInstanceOperationResultDTO(
                    success=False,
                    message="Craft instance not found",
                    instance_id=instance_id,
                    details={}
                )
            
            instance.reject_completion(rejection_data.rejected_by_user_id, rejection_data.reason)
            await self.instance_repository.update(instance)
            
            return CraftInstanceOperationResultDTO(
                success=True,
                message="Craft instance rejected successfully",
                instance_id=instance_id,
                details={"status": "rejected", "reason": rejection_data.reason}
            )
            
        except Exception as e:
            return CraftInstanceOperationResultDTO(
                success=False,
                message=f"Failed to reject instance: {str(e)}",
                instance_id=instance_id,
                details={}
            )
    
    async def get_statistics(self, 
                           order_no: Optional[str] = None,
                           worker_user_id: Optional[int] = None,
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None) -> CraftInstanceStatisticsDTO:
        """Get craft instance statistics."""
        stats = await self.instance_repository.get_statistics(
            order_no=order_no,
            worker_user_id=worker_user_id,
            start_date=start_date,
            end_date=end_date
        )
        
        return CraftInstanceStatisticsDTO(**stats)
    
    async def _build_instance_dto(self, instance: OrderCraftRouteInstance) -> CraftInstanceResponseDTO:
        """Build craft instance response DTO."""
        # Get worker name
        worker_name = None
        if instance.worker:
            worker_name = instance.worker.full_name or instance.worker.username
        
        # Get route and skill info
        craft_route_name = None
        skill_code = None
        skill_name = None
        
        if instance.order_craft_route:
            craft_route_name = instance.order_craft_route.name
            skill_code = instance.order_craft_route.skill_code
            if instance.order_craft_route.skill:
                skill_name = instance.order_craft_route.skill.name
        
        # Calculate work duration
        work_duration = instance.calculate_work_duration()
        
        return CraftInstanceResponseDTO(
            id=instance.id,
            factory_id=instance.factory_id,
            order_craft_route_id=instance.order_craft_route_id,
            completion_granularity=instance.completion_granularity.value,
            order_no=instance.order_no,
            order_part_nos=instance.get_all_part_nos(),
            order_bundle_nos=instance.get_all_bundle_nos(),
            # Legacy single part/bundle for backward compatibility
            order_part_no=instance.order_part_no,
            order_bundle_no=instance.order_bundle_no,
            worker_user_id=instance.worker_user_id,
            worker_name=worker_name,
            completed_quantity=instance.completed_quantity,
            quality_level=instance.quality_level,
            status=instance.status,
            settlement_status=instance.settlement_status.value,
            started_at=instance.started_at,
            completed_at=instance.completed_at,
            work_duration_minutes=work_duration,
            qr_code_scanned=instance.qr_code_scanned,
            scan_location=instance.scan_location,
            device_info=instance.device_info,
            measurement_data=instance.measurement_data,
            registration_data=instance.registration_data,
            notes=instance.notes,
            created_at=instance.created_at,
            updated_at=instance.updated_at,
            craft_route_name=craft_route_name,
            skill_code=skill_code,
            skill_name=skill_name
        )