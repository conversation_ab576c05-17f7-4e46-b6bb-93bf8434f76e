.orders-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-x: hidden;
}

.orders-header {
  background: #fff;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-section {
  display: flex;
  align-items: center;
  gap: 12px;

  .search-input-wrapper {
    flex: 1;

    .search-input {
      width: 100%;
      height: 40px;
      padding: 0 16px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 20px;
      font-size: 14px;
      color: #333;
      box-sizing: border-box;

      &::placeholder {
        color: #999;
      }

      &:focus {
        border-color: #667eea;
        background: #fff;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
      }
    }
  }

  .filter-btn {
    width: 60px;
    height: 40px;
    background: #667eea;
    color: #fff;
    border: none;
    border-radius: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    padding: 0;
    line-height: 1;

    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }
  }
}

.filters-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;

  .filter-group {
    margin-bottom: 12px;

    .filter-label {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      margin-bottom: 8px;
      display: block;
    }

    .status-filters {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .status-filter {
        padding: 6px 12px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 16px;
        transition: all 0.2s ease;

        &.active {
          background: #667eea;
          border-color: #667eea;

          .status-filter__text {
            color: #fff;
          }
        }

        &__text {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }

  .filter-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    button {
      background: #f8f9fa;
      color: #666;
      border: 1px solid #e9ecef;
      border-radius: 16px;

      &:active {
        opacity: 0.8;
      }
    }
  }
}

.orders-list {
  flex: 1;
  padding: 20px;
}

.order-card {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .order-card__title {
      display: flex;
      align-items: center;
      gap: 8px;

      .order-no {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }

    .order-card__date {
      font-size: 12px;
      color: #999;
    }
  }

  &__content {
    margin-bottom: 12px;
  }

  &__row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 12px;
      color: #666;
      flex-shrink: 0;
    }

    .value {
      font-size: 12px;
      color: #333;
      text-align: right;
      flex: 1;
      margin-left: 8px;
    }
  }

  &__progress {
    margin-bottom: 12px;
  }

  &__footer {
    border-top: 1px solid #f0f0f0;
    padding-top: 12px;
  }

  &__meta {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .amount {
      font-size: 16px;
      font-weight: bold;
      color: #667eea;
    }

    .deadline {
      font-size: 11px;
      color: #999;
    }
  }
}

.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
  font-size: 14px;
}

.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  color: #999;
  font-size: 12px;
}

// Responsive Design
@media (max-width: 480px) {
  .orders-header {
    padding: 16px;
  }

  .search-section {
    gap: 16rpx;

    .search-input-wrapper {
      .search-input {
        height: 70rpx;
        font-size: 28rpx;
        padding: 0 24rpx;
      }
    }

    .filter-btn {
      width: 120rpx;
      height: 70rpx;
      font-size: 26rpx;
    }
  }

  .order-card {
    padding: 12px;
    margin-bottom: 8px;

    &__header {
      .order-card__title {
        .order-no {
          font-size: 14px;
        }
      }
    }

    &__row {
      .label,
      .value {
        font-size: 11px;
      }
    }

    &__meta {
      .amount {
        font-size: 14px;
      }

      .deadline {
        font-size: 10px;
      }
    }
  }
}

@media (min-width: 768px) {
  .orders-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .orders-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;

    .order-card {
      margin-bottom: 0;
    }
  }
}
