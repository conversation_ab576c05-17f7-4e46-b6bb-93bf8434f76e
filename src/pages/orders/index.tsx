import { useState, useEffect } from 'react'
import { View, Text, Input, But<PERSON>, ScrollView } from '@tarojs/components'
import Taro from '@tarojs/taro'
import ApiService from '../../services/api'
import StatusBadge from '../../components/StatusBadge'
import ProgressBar from '../../components/ProgressBar'
import { formatDate, formatRelativeTime, formatCurrency, sortOrders, filterOrders, ORDER_STATUSES } from '../../utils/orderUtils'
import './index.scss'

interface Order {
  id: number
  order_no: string
  external_order_no: string
  skc_no: string
  factory_id: number
  owner_user_id: number
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'ON_HOLD' | 'DELAYED'
  total_amount: number
  cost: number
  price: number
  current_craft: string
  created_at: string
  started_at: string | null
  expect_finished_at: string | null
  finished_at: string | null
  completion_rate: number
}

interface FilterState {
  status: string[]
  search: string
  dateFrom: string
  dateTo: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

const Orders = () => {
  const [orders, setOrders] = useState<Order[]>([])
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState<FilterState>({
    status: [],
    search: '',
    dateFrom: '',
    dateTo: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  })
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })

  useEffect(() => {
    loadOrders()
  }, [])

  useEffect(() => {
    applyFiltersAndSort()
  }, [orders, filters])

  const loadOrders = async (page = 1, isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true)
    } else {
      setLoading(true)
    }

    try {
      const response = await ApiService.getOrderList({
        page,
        limit: pagination.limit,
        sort_by: filters.sortBy,
        sort_order: filters.sortOrder
      })

      if (page === 1) {
        setOrders(response.orders)
      } else {
        setOrders(prev => [...prev, ...response.orders])
      }

      setPagination({
        page: response.page,
        limit: response.limit,
        total: response.total,
        totalPages: response.total_pages
      })
    } catch (error) {
      console.error('获取订单列表失败:', error)
      Taro.showToast({
        title: '获取订单列表失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const applyFiltersAndSort = () => {
    let result = filterOrders(orders, {
      status: filters.status,
      search: filters.search,
      dateFrom: filters.dateFrom,
      dateTo: filters.dateTo
    })

    result = sortOrders(result, filters.sortBy, filters.sortOrder)
    setFilteredOrders(result)
  }

  const handleRefresh = () => {
    loadOrders(1, true)
  }

  const handleLoadMore = () => {
    if (pagination.page < pagination.totalPages && !loading) {
      loadOrders(pagination.page + 1)
    }
  }

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }))
  }

  const handleStatusFilter = (status: string) => {
    setFilters(prev => ({
      ...prev,
      status: prev.status.includes(status)
        ? prev.status.filter(s => s !== status)
        : [...prev.status, status]
    }))
  }

  const clearFilters = () => {
    setFilters({
      status: [],
      search: '',
      dateFrom: '',
      dateTo: '',
      sortBy: 'created_at',
      sortOrder: 'desc'
    })
  }

  const navigateToDetail = (orderId: number) => {
    Taro.navigateTo({
      url: `/pages/order-detail/index?id=${orderId}`
    })
  }

  const renderOrderCard = (order: Order) => (
    <View
      key={order.id}
      className="order-card"
      onClick={() => navigateToDetail(order.id)}
    >
      <View className="order-card__header">
        <View className="order-card__title">
          <Text className="order-no">{order.order_no}</Text>
          <StatusBadge status={order.status} size="small" />
        </View>
        <Text className="order-card__date">{formatDate(order.created_at)}</Text>
      </View>

      <View className="order-card__content">
        <View className="order-card__row">
          <Text className="label">SKU:</Text>
          <Text className="value">{order.skc_no}</Text>
        </View>
        {order.external_order_no && (
          <View className="order-card__row">
            <Text className="label">客户订单号:</Text>
            <Text className="value">{order.external_order_no}</Text>
          </View>
        )}
        <View className="order-card__row">
          <Text className="label">总数量:</Text>
          <Text className="value">{order.total_amount}</Text>
        </View>
        <View className="order-card__row">
          <Text className="label">当前工艺:</Text>
          <Text className="value">{order.current_craft || '-'}</Text>
        </View>
      </View>

      <View className="order-card__progress">
        <ProgressBar
          percentage={order.completion_rate}
          label="完成进度"
          size="small"
        />
      </View>

      <View className="order-card__footer">
        <View className="order-card__meta">
          <Text className="amount">{formatCurrency(order.price)}</Text>
          {order.expect_finished_at && (
            <Text className="deadline">
              预期完成: {formatRelativeTime(order.expect_finished_at)}
            </Text>
          )}
        </View>
      </View>
    </View>
  )

  return (
    <View className="orders-container">
      {/* Search and Filter Header */}
      <View className="orders-header">
        <View className="search-section">
          <View className="search-input-wrapper">
            <Input
              className="search-input"
              placeholder="搜索订单号、SKU或工艺"
              value={filters.search}
              onInput={(e) => handleSearch(e.detail.value)}
            />
          </View>
          <Button
            className="filter-btn"
            size="mini"
            onClick={() => setShowFilters(!showFilters)}
          >
            筛选
          </Button>
        </View>

        {showFilters && (
          <View className="filters-section">
            <View className="filter-group">
              <Text className="filter-label">状态:</Text>
              <View className="status-filters">
                {ORDER_STATUSES.map(status => (
                  <View
                    key={status.value}
                    className={`status-filter ${filters.status.includes(status.value) ? 'active' : ''}`}
                    onClick={() => handleStatusFilter(status.value)}
                  >
                    <Text className="status-filter__text">{status.label}</Text>
                  </View>
                ))}
              </View>
            </View>

            <View className="filter-actions">
              <Button size="mini" onClick={clearFilters}>清除筛选</Button>
              <Button size="mini" onClick={() => setShowFilters(false)}>收起</Button>
            </View>
          </View>
        )}
      </View>

      {/* Orders List */}
      <ScrollView
        className="orders-list"
        scrollY
        refresherEnabled
        refresherTriggered={refreshing}
        onRefresherRefresh={handleRefresh}
        onScrollToLower={handleLoadMore}
      >
        {loading && filteredOrders.length === 0 ? (
          <View className="loading-container">
            <Text>加载中...</Text>
          </View>
        ) : filteredOrders.length === 0 ? (
          <View className="empty-container">
            <Text>暂无订单数据</Text>
          </View>
        ) : (
          <>
            {filteredOrders.map(renderOrderCard)}
            {pagination.page < pagination.totalPages && (
              <View className="load-more">
                <Text>加载更多...</Text>
              </View>
            )}
          </>
        )}
      </ScrollView>
    </View>
  )
}

export default Orders
