import { useState, useEffect } from 'react'
import { View, Text, But<PERSON>, ScrollView } from '@tarojs/components'
import Taro from '@tarojs/taro'
import ApiService from '../../services/api'
import './index.scss'

interface UserInfo {
  id: string
  username: string
  name: string
  role: 'manager' | 'worker'
}

interface StatisticsData {
  manager?: {
    inProgressOrders: number
    todayIncomingOrders: number
    todayCost: number
  }
  worker?: {
    weeklyFinishedWork: number
    dailyFinishedWork: number
    settledSalary: number
    unsettledSalary: number
    disputeSalaryCount: number
  }
}

const Home = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null)
  const [statistics, setStatistics] = useState<StatisticsData>({})
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const savedUserInfo = Taro.getStorageSync('userInfo')
    if (savedUserInfo) {
      setUserInfo(savedUserInfo)
      loadStatistics(savedUserInfo.role)
    }
  }, [])

  const loadStatistics = async (role: 'manager' | 'worker') => {
    setLoading(true)
    try {
      if (role === 'manager') {
        const data = await ApiService.getManagerStatistics()
        setStatistics({ manager: data })
      } else {
        const data = await ApiService.getWorkerStatistics()
        setStatistics({ worker: data })
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = () => {
    Taro.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          Taro.removeStorageSync('token')
          Taro.removeStorageSync('userInfo')
          Taro.redirectTo({
            url: '/pages/login/index'
          })
        }
      }
    })
  }

  const navigateTo = (page: string) => {
    Taro.navigateTo({
      url: `/pages/${page}/index`
    })
  }

  const renderManagerStats = () => {
    const { manager } = statistics
    if (!manager) return null

    return (
      <View className="stats-section">
        <View className="stat-card">
          <Text className="stat-number">{manager.inProgressOrders}</Text>
          <Text className="stat-label">进行中订单</Text>
        </View>
        <View className="stat-card">
          <Text className="stat-number">{manager.todayIncomingOrders}</Text>
          <Text className="stat-label">今日新增订单</Text>
        </View>
        <View className="stat-card">
          <Text className="stat-number">¥{manager.todayCost.toLocaleString()}</Text>
          <Text className="stat-label">今日成本</Text>
        </View>
      </View>
    )
  }

  const renderWorkerStats = () => {
    const { worker } = statistics
    if (!worker) return null

    return (
      <View className="stats-section">
        <View className="stat-row">
          <View className="stat-card">
            <Text className="stat-number">{worker.weeklyFinishedWork}</Text>
            <Text className="stat-label">周完成工作</Text>
          </View>
          <View className="stat-card">
            <Text className="stat-number">{worker.dailyFinishedWork}</Text>
            <Text className="stat-label">今日完成工作</Text>
          </View>
        </View>
        <View className="stat-row">
          <View className="stat-card">
            <Text className="stat-number">¥{worker.settledSalary.toLocaleString()}</Text>
            <Text className="stat-label">已结算工资</Text>
          </View>
          <View className="stat-card">
            <Text className="stat-number">¥{worker.unsettledSalary.toLocaleString()}</Text>
            <Text className="stat-label">待结算工资</Text>
          </View>
        </View>
        <View className="stat-row">
          <View className="stat-card dispute">
            <Text className="stat-number">{worker.disputeSalaryCount}</Text>
            <Text className="stat-label">争议工资</Text>
          </View>
        </View>
      </View>
    )
  }

  const functionButtons = [
    { key: 'scan-register', title: '扫菲登记', page: 'scan-register' },
    { key: 'new-order', title: '新建订单', page: 'new-order' },
    { key: 'orders', title: '订单', page: 'orders' },
    { key: 'cutting', title: '裁床', page: 'cutting' },
    { key: 'employee-management', title: '员工管理', page: 'employee-management' },
    { key: 'department-management', title: '部门管理', page: 'department-management' },
    { key: 'bill-management', title: '账单管理', page: 'bill-management' }
  ]

  return (
    <View className="home-container">
      <View className="home-header">
        <View className="user-info">
          <Text className="welcome-text">欢迎回来</Text>
          <Text className="user-name">{userInfo?.name || '用户'}</Text>
          <Text className="user-role">
            {userInfo?.role === 'manager' ? '管理员' : '工人'}
          </Text>
        </View>
        <Button className="logout-btn" size="mini" onClick={handleLogout}>
          退出
        </Button>
      </View>

      {loading ? (
        <View className="loading-container">
          <Text>加载中...</Text>
        </View>
      ) : (
        <>
          {userInfo?.role === 'manager' ? renderManagerStats() : renderWorkerStats()}
          
          <View className="functions-section">
            <View className="section-header">
              <Text className="section-title">功能菜单</Text>
            </View>
            <View className="functions-grid">
              {functionButtons.map((func) => (
                <View 
                  key={func.key}
                  className="function-item"
                  onClick={() => navigateTo(func.page)}
                >
                  <View className="function-icon">
                    <Text className="icon-text">{func.title.charAt(0)}</Text>
                  </View>
                  <Text className="function-title">{func.title}</Text>
                </View>
              ))}
            </View>
          </View>
        </>
      )}
    </View>
  )
}

export default Home