.home-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

.home-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .user-info {
    .welcome-text {
      display: block;
      font-size: 14px;
      color: #666;
      margin-bottom: 4px;
    }
    
    .user-name {
      display: block;
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 4px;
    }
    
    .user-role {
      display: block;
      font-size: 12px;
      color: #667eea;
      background: #e8f0fe;
      padding: 2px 8px;
      border-radius: 12px;
      width: fit-content;
    }
  }
  
  .logout-btn {
    background: #ff4757;
    color: #fff;
    border: none;
    
    &:active {
      opacity: 0.8;
    }
  }
}

.stats-section {
  margin-bottom: 20px;
  
  .stat-row {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .stat-card {
    flex: 1;
    background: #fff;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    &.dispute {
      background: linear-gradient(135deg, #ff6b6b, #ff5252);
      
      .stat-number,
      .stat-label {
        color: #fff;
      }
    }
    
    .stat-number {
      display: block;
      font-size: 24px;
      font-weight: bold;
      color: #667eea;
      margin-bottom: 4px;
    }
    
    .stat-label {
      display: block;
      font-size: 12px;
      color: #666;
    }
  }
}

.functions-section {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .section-header {
    margin-bottom: 16px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
  }
  
  .functions-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    
    .function-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px 12px;
      border-radius: 12px;
      background: #f8f9ff;
      border: 1px solid #e8f0fe;
      transition: all 0.2s ease;
      
      &:active {
        transform: scale(0.95);
        background: #e8f0fe;
      }
      
      .function-icon {
        width: 48px;
        height: 48px;
        border-radius: 24px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        
        .icon-text {
          font-size: 20px;
          font-weight: bold;
          color: #fff;
        }
      }
      
      .function-title {
        font-size: 12px;
        color: #333;
        text-align: center;
        line-height: 1.2;
      }
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #999;
  font-size: 14px;
}