import { useState } from 'react'
import { View, Text, Input, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import ApiService from '../../services/api'
import './index.scss'

const Login = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [loading, setLoading] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleLogin = async () => {
    if (!formData.username || !formData.password) {
      Taro.showToast({
        title: '请输入用户名和密码',
        icon: 'none'
      })
      return
    }

    setLoading(true)
    try {
      const response = await ApiService.login(formData)
      console.log('resp ', response)

      // 保存token和用户信息
      Taro.setStorageSync('token', response.access_token)
      Taro.setStorageSync('factory_context', response.factory_context)

      // 获取完整的用户信息
      const currentUser = await ApiService.getCurrentUser()
      Taro.setStorageSync('userInfo', currentUser)

      Taro.showToast({
        title: '登录成功',
        icon: 'success'
      })

      // 跳转到首页
      setTimeout(() => {
        Taro.navigateTo({
          url: '/pages/home/<USER>'
        })
      }, 1500)
    } catch (error) {
      console.error('登录失败:', error)
      Taro.showToast({
        title: '登录失败',
        icon: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <View className='login-container'>
      <View className='login-header'>
        <Text className='login-title'>MES Ticket</Text>
        <Text className='login-subtitle'>制造执行系统 - 请登录您的账号</Text>
      </View>

      <View className='login-form'>
        <View className='form-item'>
          <Text className='form-label'>用户名</Text>
          <Input
            className='form-input'
            type='text'
            placeholder='请输入用户名或邮箱'
            value={formData.username}
            onInput={(e) => handleInputChange('username', e.detail.value)}
          />
        </View>

        <View className='form-item'>
          <Text className='form-label'>密码</Text>
          <Input
            className='form-input'
            password
            placeholder='请输入登录密码'
            value={formData.password}
            onInput={(e) => handleInputChange('password', e.detail.value)}
          />
        </View>

        <Button
          className={`login-btn ${loading ? 'button-loading' : ''}`}
          loading={loading}
          disabled={loading}
          onClick={handleLogin}
        >
          {loading ? '登录中...' : '立即登录'}
        </Button>
      </View>
    </View>
  )
}

export default Login
