.order-detail-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.tabs-header {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tabs {
  display: flex;

  .tab {
    flex: 1;
    padding: 16px;
    text-align: center;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;

    &.active {
      border-bottom-color: #667eea;

      .tab-text {
        color: #667eea;
        font-weight: bold;
      }
    }

    &-text {
      font-size: 14px;
      color: #666;
    }
  }
}

.detail-content {
  flex: 1;
  padding: 20px;
}

.detail-section {
  margin-bottom: 20px;

  .section-header {
    margin-bottom: 12px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
  }
}

// Summary Section Styles
.summary-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .summary-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .order-title {
      display: flex;
      align-items: center;
      gap: 12px;

      .order-no {
        font-size: 20px;
        font-weight: bold;
        color: #333;
      }
    }

    .created-date {
      font-size: 12px;
      color: #999;
    }
  }

  .summary-content {
    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 20px;

      .info-item {
        .info-label {
          display: block;
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        }

        .info-value {
          display: block;
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }
      }
    }

    .progress-section {
      margin-bottom: 20px;

      > * {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .timeline-section {
      border-top: 1px solid #f0f0f0;
      padding-top: 16px;

      .timeline-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .timeline-label {
          font-size: 12px;
          color: #666;
        }

        .timeline-value {
          font-size: 12px;
          color: #333;
        }
      }
    }
  }
}

// Sizes Section Styles
.sizes-list {
  .size-card {
    background: #fff;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .size-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .size-name {
        font-size: 18px;
        font-weight: bold;
        color: #333;
      }

      .size-code {
        font-size: 12px;
        color: #999;
      }
    }

    .size-content {
      .quantity-info {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;
        margin-bottom: 16px;

        .quantity-item {
          text-align: center;

          .quantity-label {
            display: block;
            font-size: 10px;
            color: #666;
            margin-bottom: 4px;
          }

          .quantity-value {
            display: block;
            font-size: 14px;
            color: #333;
            font-weight: bold;
          }
        }
      }

      .progress-info {
        margin-bottom: 12px;

        > * {
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .current-stage {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 12px;
        border-top: 1px solid #f0f0f0;

        .stage-label {
          font-size: 12px;
          color: #666;
        }

        .stage-value {
          font-size: 12px;
          color: #667eea;
          font-weight: 500;
        }
      }
    }
  }
}

// Workflow Section Styles
.workflow-timeline {
  .workflow-item {
    display: flex;
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .workflow-indicator {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 16px;

      .workflow-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-bottom: 8px;

        &.pending {
          background: #D9D9D9;
        }

        &.in_progress {
          background: #1890FF;
        }

        &.completed {
          background: #52C41A;
        }

        &.skipped {
          background: #FAAD14;
        }
      }

      .workflow-line {
        width: 2px;
        flex: 1;
        background: #f0f0f0;
        min-height: 40px;
      }
    }

    .workflow-content {
      flex: 1;
      background: #fff;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .craft-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .craft-name {
          font-size: 16px;
          font-weight: bold;
          color: #333;
        }
      }

      .craft-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .craft-code {
          font-size: 12px;
          color: #666;
        }

        .required-badge {
          font-size: 10px;
          color: #ff4757;
          background: #ffe8e8;
          padding: 2px 6px;
          border-radius: 8px;
        }
      }

      .time-info {
        display: flex;
        gap: 16px;
        margin-bottom: 8px;

        .time-item {
          font-size: 12px;
          color: #666;
        }
      }

      .time-range {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }

        .time-label {
          font-size: 12px;
          color: #666;
        }

        .time-value {
          font-size: 12px;
          color: #333;
        }
      }
    }
  }
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: #999;
  font-size: 14px;
}

// Responsive Design
@media (max-width: 480px) {
  .detail-content {
    padding: 16px;
  }

  .summary-card {
    padding: 16px;

    .summary-header {
      .order-title {
        .order-no {
          font-size: 18px;
        }
      }
    }

    .summary-content {
      .info-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }
    }
  }

  .size-card {
    padding: 12px;

    .size-content {
      .quantity-info {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
      }
    }
  }

  .workflow-content {
    padding: 12px;

    .craft-header {
      .craft-name {
        font-size: 14px;
      }
    }
  }
}

@media (min-width: 768px) {
  .order-detail-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .detail-content {
    padding: 32px;
  }

  .summary-card {
    .summary-content {
      .info-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }

  .sizes-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;

    .size-card {
      margin-bottom: 0;
    }
  }
}

// Enhanced touch targets for mobile
@media (max-width: 768px) {
  .tab {
    padding: 20px 16px;
    min-height: 44px; // iOS recommended touch target
  }

  .order-card {
    min-height: 44px;

    &:active {
      transform: scale(0.98);
    }
  }
}
