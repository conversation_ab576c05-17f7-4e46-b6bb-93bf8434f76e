.employee-management {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .factory-info {
    flex: 1;

    .factory-name {
      display: block;
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 6px;
    }

    .employee-count {
      display: block;
      font-size: 14px;
      color: #666;
    }
  }

  .action-buttons {
    display: flex;
    gap: 20rpx;
    align-items: center;

    .invite-btn,
    .add-btn {
      border: none;
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 13px;
      font-weight: 500;
      width: 140rpx;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 0;

      &:active {
        opacity: 0.8;
        transform: scale(0.98);
      }
    }

    .invite-btn {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: #fff;
    }

    .add-btn {
      background: linear-gradient(135deg, #28a745, #20c997);
      color: #fff;
    }
  }
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .search-row {
    display: flex;
    gap: 20rpx;
    margin-bottom: 16px;
    align-items: center;

    .search-input {
      width: 580rpx;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 8px 12px;
      font-size: 14px;
      transition: border-color 0.2s ease;
      height: 36px;
      box-sizing: border-box;

      &:focus {
        border-color: #667eea;
        outline: none;
      }
    }

    .search-btn {
      width: 130rpx;
      background: #667eea;
      color: #fff;
      border: none;
      border-radius: 6px;
      padding: 0;
      font-size: 12px;
      font-weight: 500;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        opacity: 0.8;
        transform: scale(0.98);
      }
    }
  }

  .filter-row {
    display: flex;
    align-items: center;

    .filter-label {
      font-size: 14px;
      color: #666;
      margin-right: 12px;
    }

    .role-filters {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;

      .filter-item {
        padding: 6px 12px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 16px;
        font-size: 12px;
        color: #666;
        cursor: pointer;
        transition: all 0.2s ease;

        &.active {
          background: #667eea;
          color: #fff;
          border-color: #667eea;
        }

        &:active {
          opacity: 0.8;
        }
      }
    }
  }
}

.employee-list {
  height: calc(100vh - 320px);
  min-height: 400px;

  .loading-container,
  .empty-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #999;
    font-size: 14px;
    background: #fff;
    border-radius: 12px;
    margin-bottom: 12px;

    &::before {
      content: '';
      width: 48px;
      height: 48px;
      background: #f0f0f0;
      border-radius: 50%;
      margin-bottom: 12px;
    }
  }

  .employee-item {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    display: flex;
    gap: 16px;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      border-color: #e0e0e0;
    }

    .employee-avatar {
      width: 56px;
      height: 56px;
      border-radius: 28px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

      .avatar-text {
        font-size: 20px;
        font-weight: bold;
        color: #fff;
      }
    }

    .employee-info {
      flex: 1;

      .name-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .employee-name {
          font-size: 17px;
          font-weight: 600;
          color: #333;
          line-height: 1.3;
        }

        .status-badge {
          padding: 4px 10px;
          border-radius: 14px;
          font-size: 11px;
          font-weight: 500;

          &.status-active {
            background: #d4edda;
            border: 1px solid #c3e6cb;

            .status-text {
              color: #155724;
            }
          }

          &.status-suspended {
            background: #fff3cd;
            border: 1px solid #ffeaa7;

            .status-text {
              color: #856404;
            }
          }

          &.status-inactive {
            background: #f8d7da;
            border: 1px solid #f5c6cb;

            .status-text {
              color: #721c24;
            }
          }
        }
      }

      .employee-email {
        display: block;
        font-size: 14px;
        color: #666;
        margin-bottom: 10px;
        line-height: 1.4;
      }

      .role-skills-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .employee-role {
          font-size: 12px;
          font-weight: 500;
          color: #667eea;
          background: #e8f0fe;
          padding: 4px 10px;
          border-radius: 14px;
          border: 1px solid #d1e7ff;
        }

        .skills-count {
          font-size: 12px;
          color: #666;
          background: #f8f9fa;
          padding: 2px 8px;
          border-radius: 10px;
        }
      }

      .join-date {
        display: block;
        font-size: 12px;
        color: #999;
        line-height: 1.3;
      }
    }
  }
}
