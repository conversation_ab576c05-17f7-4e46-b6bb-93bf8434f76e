.department-management {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .department-info {
    .page-title {
      display: block;
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 4px;
    }
    
    .department-count {
      display: block;
      font-size: 14px;
      color: #666;
    }
  }
  
  .add-btn {
    background: #667eea;
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    
    &:active {
      opacity: 0.8;
    }
  }
}

.search-section {
  background: #fff;
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .search-row {
    display: flex;
    gap: 12px;
    
    .search-input {
      flex: 1;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 12px;
      font-size: 14px;
    }
    
    .search-btn {
      background: #667eea;
      color: #fff;
      border: none;
      border-radius: 8px;
      padding: 8px 16px;
      
      &:active {
        opacity: 0.8;
      }
    }
  }
}

.department-list {
  height: 500px;
  
  .loading-container,
  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #999;
    font-size: 14px;
  }
  
  .department-item {
    background: #fff;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 16px;
    transition: all 0.2s ease;
    
    &:active {
      transform: scale(0.98);
      opacity: 0.8;
    }
    
    .department-icon {
      width: 48px;
      height: 48px;
      border-radius: 24px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      
      .icon-text {
        font-size: 20px;
        font-weight: bold;
        color: #fff;
      }
    }
    
    .department-info {
      flex: 1;
      
      .name-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        
        .department-name {
          font-size: 16px;
          font-weight: bold;
          color: #333;
        }
        
        .status-badge {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 10px;
          
          &.status-active {
            background: #d4edda;
            
            .status-text {
              color: #155724;
            }
          }
          
          &.status-inactive {
            background: #f8d7da;
            
            .status-text {
              color: #721c24;
            }
          }
        }
      }
      
      .department-desc {
        display: block;
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        line-height: 1.4;
      }
      
      .manager-employee-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        
        .manager-info {
          font-size: 12px;
          color: #667eea;
          background: #e8f0fe;
          padding: 2px 8px;
          border-radius: 12px;
        }
        
        .employee-count {
          font-size: 12px;
          color: #666;
          background: #f8f9fa;
          padding: 2px 8px;
          border-radius: 12px;
        }
      }
      
      .create-date {
        display: block;
        font-size: 12px;
        color: #999;
      }
    }
  }
}

// Department Form Styles
.department-form {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
  
  .form-header {
    background: #fff;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .form-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
  }
  
  .form-content {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .form-group {
      margin-bottom: 20px;
      
      .form-label {
        display: block;
        font-size: 14px;
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
      }
      
      .form-input {
        width: 100%;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 12px;
        font-size: 14px;
        
        &:focus {
          border-color: #667eea;
          background: #fff;
        }
      }
      
      .form-textarea {
        width: 100%;
        min-height: 80px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 12px;
        font-size: 14px;
        resize: vertical;
        
        &:focus {
          border-color: #667eea;
          background: #fff;
        }
      }
    }
    
    .form-actions {
      display: flex;
      gap: 12px;
      margin-top: 24px;
      
      .cancel-btn {
        flex: 1;
        background: #6c757d;
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 12px;
        
        &:active {
          opacity: 0.8;
        }
      }
      
      .submit-btn {
        flex: 1;
        background: #667eea;
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 12px;
        
        &:active {
          opacity: 0.8;
        }
      }
    }
  }
}