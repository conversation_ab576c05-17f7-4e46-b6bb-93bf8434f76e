import { useState, useEffect } from 'react'
import { View, Text, Button, Input, ScrollView, Form, Textarea } from '@tarojs/components'
import Taro from '@tarojs/taro'
import ApiService from '../../services/api'
import './index.scss'

interface Department {
  id: number
  name: string
  description: string | null
  manager_id: number | null
  manager_name: string | null
  employee_count: number
  is_active: boolean
  created_at: string
  updated_at: string
}

interface DepartmentFormData {
  name: string
  description: string
  manager_id: string
}

const DepartmentManagement = () => {
  const [departments, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingDepartment, setEditingDepartment] = useState<Department | null>(null)
  const [formData, setFormData] = useState<DepartmentFormData>({
    name: '',
    description: '',
    manager_id: ''
  })

  useEffect(() => {
    loadDepartments()
  }, [])

  const loadDepartments = async () => {
    setLoading(true)
    try {
      const data = await ApiService.getDepartmentList()
      setDepartments(data)
    } catch (error) {
      console.error('获取部门列表失败:', error)
      Taro.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = () => {
    const filtered = departments.filter(dept => 
      dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (dept.description && dept.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (dept.manager_name && dept.manager_name.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    return filtered
  }

  const handleAddDepartment = () => {
    setEditingDepartment(null)
    setFormData({
      name: '',
      description: '',
      manager_id: ''
    })
    setShowAddForm(true)
  }

  const handleEditDepartment = (department: Department) => {
    setEditingDepartment(department)
    setFormData({
      name: department.name,
      description: department.description || '',
      manager_id: department.manager_id?.toString() || ''
    })
    setShowAddForm(true)
  }

  const handleDeleteDepartment = async (department: Department) => {
    const confirmed = await new Promise<boolean>((resolve) => {
      Taro.showModal({
        title: '确认删除',
        content: `确定要删除部门 "${department.name}" 吗？此操作不可撤销。`,
        success: (res) => resolve(res.confirm)
      })
    })

    if (!confirmed) return

    try {
      await ApiService.deleteDepartment(department.id)
      Taro.showToast({
        title: '删除成功',
        icon: 'success'
      })
      await loadDepartments()
    } catch (error) {
      console.error('删除失败:', error)
      Taro.showToast({
        title: '删除失败',
        icon: 'error'
      })
    }
  }

  const handleFormSubmit = async () => {
    if (!formData.name.trim()) {
      Taro.showToast({
        title: '请输入部门名称',
        icon: 'none'
      })
      return
    }

    try {
      const departmentData = {
        name: formData.name.trim(),
        description: formData.description.trim() || null,
        manager_id: formData.manager_id ? parseInt(formData.manager_id) : null
      }

      if (editingDepartment) {
        await ApiService.updateDepartment(editingDepartment.id, departmentData)
        Taro.showToast({
          title: '更新成功',
          icon: 'success'
        })
      } else {
        await ApiService.createDepartment(departmentData)
        Taro.showToast({
          title: '创建成功',
          icon: 'success'
        })
      }

      setShowAddForm(false)
      await loadDepartments()
    } catch (error) {
      console.error('保存失败:', error)
      Taro.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  }

  const handleFormCancel = () => {
    setShowAddForm(false)
    setEditingDepartment(null)
    setFormData({
      name: '',
      description: '',
      manager_id: ''
    })
  }

  const handleDepartmentAction = (department: Department) => {
    Taro.showActionSheet({
      itemList: ['查看详情', '编辑部门', '删除部门'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            showDepartmentDetails(department)
            break
          case 1:
            handleEditDepartment(department)
            break
          case 2:
            handleDeleteDepartment(department)
            break
        }
      }
    })
  }

  const showDepartmentDetails = (department: Department) => {
    const details = [
      `部门名称: ${department.name}`,
      `部门描述: ${department.description || '无'}`,
      `部门经理: ${department.manager_name || '未指定'}`,
      `员工数量: ${department.employee_count}人`,
      `状态: ${department.is_active ? '启用' : '禁用'}`,
      `创建时间: ${new Date(department.created_at).toLocaleString()}`
    ].join('\n')

    Taro.showModal({
      title: '部门详情',
      content: details,
      showCancel: false
    })
  }

  const filteredDepartments = searchTerm ? handleSearch() : departments

  if (showAddForm) {
    return (
      <View className="department-form">
        <View className="form-header">
          <Text className="form-title">
            {editingDepartment ? '编辑部门' : '添加部门'}
          </Text>
        </View>

        <Form className="form-content">
          <View className="form-group">
            <Text className="form-label">部门名称 *</Text>
            <Input
              className="form-input"
              placeholder="请输入部门名称"
              value={formData.name}
              onInput={(e) => setFormData({ ...formData, name: e.detail.value })}
            />
          </View>

          <View className="form-group">
            <Text className="form-label">部门描述</Text>
            <Textarea
              className="form-textarea"
              placeholder="请输入部门描述（可选）"
              value={formData.description}
              onInput={(e) => setFormData({ ...formData, description: e.detail.value })}
              maxlength={500}
            />
          </View>

          <View className="form-group">
            <Text className="form-label">部门经理ID</Text>
            <Input
              className="form-input"
              placeholder="请输入经理用户ID（可选）"
              type="number"
              value={formData.manager_id}
              onInput={(e) => setFormData({ ...formData, manager_id: e.detail.value })}
            />
          </View>

          <View className="form-actions">
            <Button className="cancel-btn" onClick={handleFormCancel}>
              取消
            </Button>
            <Button className="submit-btn" onClick={handleFormSubmit}>
              {editingDepartment ? '更新' : '创建'}
            </Button>
          </View>
        </Form>
      </View>
    )
  }

  return (
    <View className="department-management">
      <View className="header-section">
        <View className="department-info">
          <Text className="page-title">部门管理</Text>
          <Text className="department-count">共 {departments.length} 个部门</Text>
        </View>
        <Button className="add-btn" size="small" onClick={handleAddDepartment}>
          添加部门
        </Button>
      </View>

      <View className="search-section">
        <View className="search-row">
          <Input
            className="search-input"
            placeholder="搜索部门名称、描述或经理"
            value={searchTerm}
            onInput={(e) => setSearchTerm(e.detail.value)}
          />
          <Button className="search-btn" size="small" onClick={() => setSearchTerm(searchTerm)}>
            搜索
          </Button>
        </View>
      </View>

      <ScrollView className="department-list" scrollY>
        {loading ? (
          <View className="loading-container">
            <Text>加载中...</Text>
          </View>
        ) : filteredDepartments.length === 0 ? (
          <View className="empty-container">
            <Text>暂无部门数据</Text>
          </View>
        ) : (
          filteredDepartments.map((department) => (
            <View 
              key={department.id} 
              className="department-item"
              onClick={() => handleDepartmentAction(department)}
            >
              <View className="department-icon">
                <Text className="icon-text">
                  {department.name.charAt(0)}
                </Text>
              </View>
              
              <View className="department-info">
                <View className="name-row">
                  <Text className="department-name">{department.name}</Text>
                  <View className={`status-badge ${department.is_active ? 'status-active' : 'status-inactive'}`}>
                    <Text className="status-text">
                      {department.is_active ? '启用' : '禁用'}
                    </Text>
                  </View>
                </View>
                
                {department.description && (
                  <Text className="department-desc">{department.description}</Text>
                )}
                
                <View className="manager-employee-row">
                  <Text className="manager-info">
                    经理: {department.manager_name || '未指定'}
                  </Text>
                  <Text className="employee-count">
                    {department.employee_count} 人
                  </Text>
                </View>
                
                <Text className="create-date">
                  创建于: {new Date(department.created_at).toLocaleDateString()}
                </Text>
              </View>
            </View>
          ))
        )}
      </ScrollView>
    </View>
  )
}

export default DepartmentManagement