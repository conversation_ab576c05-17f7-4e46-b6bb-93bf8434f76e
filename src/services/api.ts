import Taro from '@tarojs/taro'

const BASE_URL = process.env.NODE_ENV === 'development'
  ? 'http://localhost:8000'
  : 'https://api.example.com'

interface ApiResponse<T = any> {
  statusCode: number
  data: T
  message: string
}

interface LoginParams {
  username: string
  password: string
}

interface LoginResponse {
  access_token: string
  token_type: string
  session_id: string
  factory_context: {
    factory_id: number
    factory_name: string
    department_id: number
    role: string
    is_manager: boolean
  }
}

class ApiService {
  private request<T = any>(options: {
    url: string
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
    data?: any
    header?: Record<string, string>
  }): Promise<ApiResponse<T>> {
    const { url, method = 'GET', data, header = {} } = options

    const token = Taro.getStorageSync('token')
    if (token) {
      header.Authorization = `Bearer ${token}`
    }

    return new Promise((resolve, reject) => {
      Taro.request({
        url: `${BASE_URL}${url}`,
        method,
        data,
        header: {
          'Content-Type': 'application/json',
          ...header
        },
        success: (res) => {
          resolve(res as any)
        },
        fail: (err) => {
          Taro.showToast({
            title: '网络错误',
            icon: 'none'
          })
          reject(err)
        }
      })
    })
  }

  // 登录
  async login(params: LoginParams): Promise<LoginResponse> {
    return new Promise((resolve, reject) => {
      Taro.request({
        url: `${BASE_URL}/api/v1/auth/token`,
        method: 'POST',
        data: params,
        header: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        success: (res) => {
          const response = res.data as LoginResponse
          resolve(response)
        },
        fail: (err) => {
          Taro.showToast({
            title: '网络错误',
            icon: 'none'
          })
          reject(err)
        }
      })
    })
  }

  // 获取用户信息
  async getUserInfo(): Promise<any> {
    const response = await this.request({
      url: '/user/info',
      method: 'GET'
    })
    return response.data
  }

  // 获取工单列表
  async getTickets(): Promise<any[]> {
    const response = await this.request<any[]>({
      url: '/tickets',
      method: 'GET'
    })
    return response.data
  }

  // 获取管理员统计数据
  async getManagerStatistics(): Promise<{
    inProgressOrders: number
    todayIncomingOrders: number
    todayCost: number
  }> {
    const response = await this.request({
      url: '/statistics/manager',
      method: 'GET'
    })
    return response.data
  }

  // 获取工人统计数据
  async getWorkerStatistics(): Promise<{
    weeklyFinishedWork: number
    dailyFinishedWork: number
    settledSalary: number
    unsettledSalary: number
    disputeSalaryCount: number
  }> {
    const response = await this.request({
      url: '/statistics/worker',
      method: 'GET'
    })
    return response.data
  }

  // 获取员工列表
  async getEmployeeList(): Promise<{
    users: Array<{
      user: {
        id: number
        username: string
        email: string
        full_name: string | null
        is_active: boolean
        avatar_url: string | null
        created_at: string
      }
      factory_role: string
      factory_status: string
      joined_at: string | null
      skills: Array<{
        skill_name: string
        proficiency_level: string
      }>
      skills_count: number
    }>
    total: number
    factory_id: number
    factory_name: string
  }> {
    const response = await this.request({
      url: '/api/v1/user-management/user-list',
      method: 'GET'
    })
    return response.data
  }

  // 搜索可用用户
  async searchAvailableUsers(params: {
    search_term?: string
    role_id?: number
    is_active?: boolean
  }): Promise<{
    users: Array<{
      id: number
      username: string
      email: string
      full_name: string | null
      is_active: boolean
    }>
    total: number
  }> {
    const response = await this.request({
      url: '/api/v1/user-management/available-users',
      method: 'GET',
      data: params
    })
    return response.data
  }

  // 创建员工
  async createEmployee(userData: {
    username: string
    email: string
    password: string
    full_name?: string
    factory_role?: string
    skills?: Array<{
      skill_name: string
      proficiency_level: string
    }>
  }): Promise<any> {
    const response = await this.request({
      url: '/api/v1/user-management/create-user',
      method: 'POST',
      data: userData
    })
    return response.data
  }

  // 更新用户信息
  async updateEmployee(userId: number, userData: {
    full_name?: string
    avatar_url?: string
    is_active?: boolean
  }): Promise<any> {
    const response = await this.request({
      url: `/api/v1/users/${userId}`,
      method: 'PUT',
      data: userData
    })
    return response.data
  }

  // 暂停员工
  async suspendEmployee(params: { user_id: number }): Promise<any> {
    const response = await this.request({
      url: '/api/v1/user-management/suspend',
      method: 'POST',
      data: params
    })
    return response.data
  }

  // 从工厂移除员工
  async removeEmployeeFromFactory(params: { user_id: number }): Promise<any> {
    const response = await this.request({
      url: '/api/v1/user-management/remove-user',
      method: 'DELETE',
      data: params
    })
    return response.data
  }

  // 获取部门列表
  async getDepartmentList(): Promise<Array<{
    id: number
    name: string
    description: string | null
    manager_id: number | null
    manager_name: string | null
    employee_count: number
    is_active: boolean
    created_at: string
    updated_at: string
  }>> {
    const response = await this.request({
      url: '/api/v1/departments',
      method: 'GET'
    })
    return response.data
  }

  // 获取角色列表
  async getRoleList(): Promise<Array<{
    id: string
    name: string
    display_name: string
    description: string | null
    permissions: string[]
    is_active: boolean
  }>> {
    const response = await this.request({
      url: '/api/v1/roles',
      method: 'GET'
    })
    return response.data
  }

  // 创建部门
  async createDepartment(departmentData: {
    name: string
    description?: string | null
    manager_id?: number | null
  }): Promise<any> {
    const response = await this.request({
      url: '/api/v1/departments',
      method: 'POST',
      data: departmentData
    })
    return response.data
  }

  // 更新部门
  async updateDepartment(departmentId: number, departmentData: {
    name?: string
    description?: string | null
    manager_id?: number | null
    is_active?: boolean
  }): Promise<any> {
    const response = await this.request({
      url: `/api/v1/departments/${departmentId}`,
      method: 'PUT',
      data: departmentData
    })
    return response.data
  }

  // 删除部门
  async deleteDepartment(departmentId: number): Promise<any> {
    const response = await this.request({
      url: `/api/v1/departments/${departmentId}`,
      method: 'DELETE'
    })
    return response.data
  }

  // 获取部门详情
  async getDepartmentById(departmentId: number): Promise<any> {
    const response = await this.request({
      url: `/api/v1/departments/${departmentId}`,
      method: 'GET'
    })
    return response.data
  }

  // 邀请用户加入工厂
  async inviteUser(inviteData: {
    email: string
    factory_role: string
    message?: string
  }): Promise<any> {
    const response = await this.request({
      url: '/api/v1/user-management/invite-user',
      method: 'POST',
      data: inviteData
    })
    return response.data
  }

  // 批量邀请用户
  async batchInviteUsers(inviteData: {
    emails: string[]
    factory_role: string
    message?: string
  }): Promise<any> {
    const response = await this.request({
      url: '/api/v1/user-management/batch-invite',
      method: 'POST',
      data: inviteData
    })
    return response.data
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<{
    id: number
    username: string
    email: string
    full_name: string | null
    is_active: boolean
    avatar_url: string | null
    created_at: string
    factory_context: {
      factory_id: number
      factory_name: string
      department_id: number
      role: string
      is_manager: boolean
    }
  }> {
    const response = await this.request({
      url: '/api/v1/auth/me',
      method: 'GET'
    })
    return response.data
  }

  // ==================== ORDER MANAGEMENT APIs ====================

  // 获取订单列表
  async getOrderList(params?: {
    status?: string[]
    factory_id?: number
    date_from?: string
    date_to?: string
    search?: string
    page?: number
    limit?: number
    sort_by?: string
    sort_order?: 'asc' | 'desc'
  }): Promise<{
    orders: Array<{
      id: number
      order_no: string
      external_order_no: string
      skc_no: string
      factory_id: number
      owner_user_id: number
      status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'ON_HOLD' | 'DELAYED'
      total_amount: number
      cost: number
      price: number
      current_craft: string
      created_at: string
      started_at: string | null
      expect_finished_at: string | null
      finished_at: string | null
      completion_rate: number
    }>
    total: number
    page: number
    limit: number
    total_pages: number
  }> {
    const response = await this.request({
      url: '/api/v1/orders/',
      method: 'GET',
      data: params
    })
    return response.data
  }

  // 获取订单筛选选项
  async getOrderFilters(): Promise<{
    factories: Array<{ id: number; name: string }>
    users: Array<{ id: number; name: string }>
    statuses: Array<{ value: string; label: string; color: string }>
    crafts: Array<{ code: string; name: string }>
  }> {
    const response = await this.request({
      url: '/api/v1/orders/filters/',
      method: 'GET'
    })
    return response.data
  }

  // 获取订单详情
  async getOrderDetail(orderId: number): Promise<{
    id: number
    order_no: string
    external_order_no: string
    skc_no: string
    factory_id: number
    factory_name: string
    owner_user_id: number
    owner_name: string
    status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'ON_HOLD' | 'DELAYED'
    total_amount: number
    cost: number
    price: number
    current_craft: string
    created_at: string
    started_at: string | null
    expect_finished_at: string | null
    finished_at: string | null
    completion_rate: number
    production_progress: number
  }> {
    const response = await this.request({
      url: `/api/v1/orders/${orderId}/`,
      method: 'GET'
    })
    return response.data
  }

  // 获取订单尺码明细
  async getOrderLines(orderId: number): Promise<Array<{
    id: number
    order_id: number
    order_line_no: string
    size: string
    amount: number
    produced_amount: number
    completed_amount: number
    current_craft_route_code: string
    production_progress: number
    completion_progress: number
    remaining_quantity: number
  }>> {
    const response = await this.request({
      url: `/api/v1/orders/${orderId}/lines/`,
      method: 'GET'
    })
    return response.data
  }

  // 获取订单工艺流程
  async getOrderCrafts(orderId: number): Promise<Array<{
    id: number
    order_no: string
    craft_code: string
    craft_name: string
    order: number
    status: 'pending' | 'in_progress' | 'completed' | 'skipped'
    is_required: boolean
    is_active: boolean
    estimated_duration_hours: number
    actual_duration_hours: number
    start_time: string | null
    end_time: string | null
    routes: Array<{
      id: number
      order_craft_id: number
      skill_code: string
      skill_name: string
      order: number
      assigned_user_id: number | null
      assigned_user_name: string | null
      price: number
      total_cost: number
      quality_score: number | null
      measurement_types: string[]
      registration_types: string[]
      measurement_data: any
      registration_data: any
    }>
  }>> {
    const response = await this.request({
      url: `/api/v1/orders/${orderId}/crafts/`,
      method: 'GET'
    })
    return response.data
  }

  // 获取订单部件信息
  async getOrderParts(orderId: number): Promise<Array<{
    id: number
    order_id: number
    part_type: 'FRONT_BODY' | 'BACK_BODY' | 'SLEEVE' | 'COLLAR' | 'CUFF' | 'POCKET'
    part_sequence: number
    status: 'PLANNED' | 'CUTTING' | 'SEWING' | 'QUALITY_CHECK' | 'COMPLETED'
    machine_id: number | null
    machine_name: string | null
    process_notes: string | null
    bundles: Array<{
      id: number
      order_part_id: number
      size: string
      quantity: number
      cutting_status: string
      sewing_status: string
      qc_status: string
      assigned_cutter_id: number | null
      assigned_cutter_name: string | null
      assigned_sewer_id: number | null
      assigned_sewer_name: string | null
      assigned_qc_id: number | null
      assigned_qc_name: string | null
    }>
  }>> {
    const response = await this.request({
      url: `/api/v1/orders/${orderId}/parts/`,
      method: 'GET'
    })
    return response.data
  }

  // 获取订单实时进度
  async getOrderProgress(orderId: number): Promise<{
    order_id: number
    overall_progress: number
    production_progress: number
    completion_progress: number
    current_stage: string
    estimated_completion: string | null
    delays: Array<{
      stage: string
      delay_hours: number
      reason: string
    }>
    quality_metrics: {
      average_quality_score: number
      defect_rate: number
      rework_rate: number
    }
    time_metrics: {
      planned_hours: number
      actual_hours: number
      efficiency_rate: number
    }
  }> {
    const response = await this.request({
      url: `/api/v1/orders/${orderId}/progress/`,
      method: 'GET'
    })
    return response.data
  }
}

export default new ApiService()
