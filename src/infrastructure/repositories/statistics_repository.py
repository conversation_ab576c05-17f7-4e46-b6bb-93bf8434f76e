from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, date, timedelta, timezone
from decimal import Decimal
from sqlalchemy import select, func, and_, or_, desc, text
from sqlalchemy.orm import selectinload
from sqlalchemy.ext.asyncio import AsyncSession

from src.infrastructure.logging.logger import get_logger
from src.domain.entities.order_craft_route_instance import OrderCraftRouteInstance
from src.domain.entities.order import Order, OrderStatus
from src.domain.entities.order_line import OrderLine
from src.domain.entities.user import User
from src.domain.entities.user_factory import UserFactory, UserFactoryStatus
from src.domain.entities.factory import Factory
from src.domain.entities.department import Department

logger = get_logger(__name__)


class StatisticsRepository:
    """Repository for statistics data operations."""

    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def get_daily_scan_count(self, factory_id: int, target_date: date) -> Dict[str, int]:
        """Get scan count for target date and previous day."""
        async with self.session_factory() as session:
            # Calculate date ranges
            start_of_target = datetime.combine(target_date, datetime.min.time())
            end_of_target = datetime.combine(target_date, datetime.max.time())
            
            previous_date = target_date - timedelta(days=1)
            start_of_previous = datetime.combine(previous_date, datetime.min.time())
            end_of_previous = datetime.combine(previous_date, datetime.max.time())
            
            # Count scans for target date
            target_query = select(func.count(OrderCraftRouteInstance.id)).where(
                and_(
                    OrderCraftRouteInstance.factory_id == factory_id,
                    OrderCraftRouteInstance.qr_code_scanned.is_not(None),
                    OrderCraftRouteInstance.created_at >= start_of_target,
                    OrderCraftRouteInstance.created_at <= end_of_target
                )
            )
            target_result = await session.execute(target_query)
            target_count = target_result.scalar() or 0
            
            # Count scans for previous day
            previous_query = select(func.count(OrderCraftRouteInstance.id)).where(
                and_(
                    OrderCraftRouteInstance.factory_id == factory_id,
                    OrderCraftRouteInstance.qr_code_scanned.is_not(None),
                    OrderCraftRouteInstance.created_at >= start_of_previous,
                    OrderCraftRouteInstance.created_at <= end_of_previous
                )
            )
            previous_result = await session.execute(previous_query)
            previous_count = previous_result.scalar() or 0
            
            return {
                "current": target_count,
                "previous": previous_count
            }
    
    async def get_daily_completed_orders(self, factory_id: int, target_date: date) -> Dict[str, int]:
        """Get completed orders count for target date and previous day."""
        async with self.session_factory() as session:
            # Calculate date ranges
            start_of_target = datetime.combine(target_date, datetime.min.time())
            end_of_target = datetime.combine(target_date, datetime.max.time())
            
            previous_date = target_date - timedelta(days=1)
            start_of_previous = datetime.combine(previous_date, datetime.min.time())
            end_of_previous = datetime.combine(previous_date, datetime.max.time())
            
            # Count completed orders for target date
            target_query = select(func.count(Order.id)).where(
                and_(
                    Order.factory_id == factory_id,
                    Order.status == OrderStatus.COMPLETED,
                    Order.updated_at >= start_of_target,
                    Order.updated_at <= end_of_target
                )
            )
            target_result = await session.execute(target_query)
            target_count = target_result.scalar() or 0
            
            # Count completed orders for previous day
            previous_query = select(func.count(Order.id)).where(
                and_(
                    Order.factory_id == factory_id,
                    Order.status == OrderStatus.COMPLETED,
                    Order.updated_at >= start_of_previous,
                    Order.updated_at <= end_of_previous
                )
            )
            previous_result = await session.execute(previous_query)
            previous_count = previous_result.scalar() or 0
            
            return {
                "current": target_count,
                "previous": previous_count
            }
    
    async def get_daily_production_value(self, factory_id: int, target_date: date) -> Dict[str, Decimal]:
        """Get production value for target date and previous day based on completed instances."""
        async with self.session_factory() as session:
            # Calculate date ranges
            start_of_target = datetime.combine(target_date, datetime.min.time())
            end_of_target = datetime.combine(target_date, datetime.max.time())
            
            previous_date = target_date - timedelta(days=1)
            start_of_previous = datetime.combine(previous_date, datetime.min.time())
            end_of_previous = datetime.combine(previous_date, datetime.max.time())
            
            # Calculate production value for target date
            # This is a simplified calculation - you may want to adjust based on your business logic
            target_query = select(
                func.coalesce(func.sum(OrderCraftRouteInstance.completed_quantity * 10), 0)
            ).where(
                and_(
                    OrderCraftRouteInstance.factory_id == factory_id,
                    OrderCraftRouteInstance.status == "completed",
                    OrderCraftRouteInstance.completed_at >= start_of_target,
                    OrderCraftRouteInstance.completed_at <= end_of_target
                )
            )
            target_result = await session.execute(target_query)
            target_value = Decimal(str(target_result.scalar() or 0))

            # Calculate production value for previous day
            previous_query = select(
                func.coalesce(func.sum(OrderCraftRouteInstance.completed_quantity * 10), 0)
            ).where(
                and_(
                    OrderCraftRouteInstance.factory_id == factory_id,
                    OrderCraftRouteInstance.status == "completed",
                    OrderCraftRouteInstance.completed_at >= start_of_previous,
                    OrderCraftRouteInstance.completed_at <= end_of_previous
                )
            )
            previous_result = await session.execute(previous_query)
            previous_value = Decimal(str(previous_result.scalar() or 0))
            
            return {
                "current": target_value,
                "previous": previous_value
            }
    
    async def get_recent_scan_records(self, factory_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent scan records with worker information."""
        async with self.session_factory() as session:
            query = (
                select(OrderCraftRouteInstance, User.full_name, User.username)
                .join(User, OrderCraftRouteInstance.worker_user_id == User.id)
                .where(
                    and_(
                        OrderCraftRouteInstance.factory_id == factory_id,
                        OrderCraftRouteInstance.qr_code_scanned.is_not(None)
                    )
                )
                .order_by(desc(OrderCraftRouteInstance.created_at))
                .limit(limit)
            )
            
            result = await session.execute(query)
            records = []
            
            for instance, full_name, username in result:
                # Calculate time ago
                now = datetime.now(timezone.utc)
                time_diff = now - instance.created_at

                if time_diff.total_seconds() < 60:
                    time_ago = "刚刚"
                elif time_diff.total_seconds() < 3600:
                    minutes = int(time_diff.total_seconds() / 60)
                    time_ago = f"{minutes}分钟前"
                elif time_diff.total_seconds() < 86400:
                    hours = int(time_diff.total_seconds() / 3600)
                    time_ago = f"{hours}小时前"
                else:
                    days = time_diff.days
                    time_ago = f"{days}天前"

                records.append({
                    "worker_name": full_name or username,
                    "operation_description": f"扫码完成 - 数量: {instance.completed_quantity}",
                    "scan_time": instance.created_at,
                    "time_ago_text": time_ago,
                    "status_color": "green" if instance.status == "completed" else "blue"
                })
            
            return records
    
    async def get_factory_info(self, factory_id: int) -> Optional[Dict[str, Any]]:
        """Get factory information."""
        async with self.session_factory() as session:
            query = select(Factory).where(Factory.id == factory_id)
            result = await session.execute(query)
            factory = result.scalar_one_or_none()

            if factory:
                return {
                    "id": factory.id,
                    "name": factory.name,
                    "code": factory.code
                }
            return None

    async def get_active_factory_members(self, factory_id: int) -> List[Dict[str, Any]]:
        """Get active members of a factory for online employee calculation."""
        async with self.session_factory() as session:
            query = (
                select(UserFactory, User, Department)
                .join(User, UserFactory.user_id == User.id)
                .outerjoin(Department, UserFactory.department_id == Department.id)
                .where(
                    and_(
                        UserFactory.factory_id == factory_id,
                        UserFactory.status == UserFactoryStatus.APPROVED,
                        User.is_active == True
                    )
                )
                .options(
                    selectinload(UserFactory.user),
                    selectinload(UserFactory.department)
                )
            )

            result = await session.execute(query)
            members = []

            for user_factory, user, department in result:
                members.append({
                    "user_id": user.id,
                    "username": user.username,
                    "full_name": user.full_name,
                    "department_name": department.name if department else None,
                    "role": user_factory.role.value if user_factory.role else None,
                    "employee_id": user_factory.employee_id
                })

            return members
