from typing import List, Optional, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_, or_, func
from sqlalchemy.orm import selectinload
from src.domain.entities.order_bundle import OrderBundle, BundleStatus
from src.application.interfaces.order_bundle_repository_interface import OrderBundleRepositoryInterface
from typing import Optional as TypingOptional


class OrderBundleRepository(OrderBundleRepositoryInterface):
    """SQLAlchemy implementation of order bundle repository."""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        self.session_factory = session_factory
    
    async def create(self, order_bundle: OrderBundle) -> OrderBundle:
        async with self.session_factory() as session:
            session.add(order_bundle)
            await session.commit()
            await session.refresh(order_bundle)
            
            # Re-query with eager loading to avoid DetachedInstanceError
            result = await session.execute(
                select(OrderBundle)
                .options(
                    selectinload(OrderBundle.order_part),
                    selectinload(OrderBundle.cutter),
                    selectinload(OrderBundle.sewer),
                    selectinload(OrderBundle.qc_user)
                )
                .where(OrderBundle.id == order_bundle.id)
            )
            loaded_bundle = result.scalar_one()
            
            # Expunge from session to make it detached but with loaded relationships
            session.expunge(loaded_bundle)
            return loaded_bundle
    
    async def get_by_id(self, order_bundle_id: int) -> Optional[OrderBundle]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderBundle)
                .options(
                    selectinload(OrderBundle.order_part),
                    selectinload(OrderBundle.cutter),
                    selectinload(OrderBundle.sewer),
                    selectinload(OrderBundle.qc_user)
                )
                .where(OrderBundle.id == order_bundle_id)
            )
            return result.scalar_one_or_none()
    
    async def get_by_order_bundle_no_and_factory(self, order_bundle_no: str, factory_id: int, order_no: str, session: TypingOptional[AsyncSession] = None) -> Optional[OrderBundle]:
        if session is not None:
            result = await session.execute(
                select(OrderBundle)
                .options(
                    selectinload(OrderBundle.order_part),
                    selectinload(OrderBundle.cutter),
                    selectinload(OrderBundle.sewer),
                    selectinload(OrderBundle.qc_user)
                )
                .where(and_(
                    OrderBundle.order_bundle_no == order_bundle_no,
                    OrderBundle.factory_id == factory_id,
                    OrderBundle.order_no == order_no
                ))
            )
            return result.scalar_one_or_none()
        else:
            async with self.session_factory() as session:
                result = await session.execute(
                    select(OrderBundle)
                    .options(
                        selectinload(OrderBundle.order_part),
                        selectinload(OrderBundle.cutter),
                        selectinload(OrderBundle.sewer),
                        selectinload(OrderBundle.qc_user)
                    )
                    .where(and_(
                        OrderBundle.order_bundle_no == order_bundle_no,
                        OrderBundle.factory_id == factory_id,
                        OrderBundle.order_no == order_no
                    ))
                )
                return result.scalar_one_or_none()
    
    async def get_by_order_part_no_and_factory(self, order_part_no: str, factory_id: int, order_no: str, session: TypingOptional[AsyncSession] = None) -> List[OrderBundle]:
        if session is not None:
            result = await session.execute(
                select(OrderBundle)
                .options(
                    selectinload(OrderBundle.order_part),
                    selectinload(OrderBundle.cutter),
                    selectinload(OrderBundle.sewer),
                    selectinload(OrderBundle.qc_user)
                )
                .where(and_(
                    OrderBundle.order_part_no == order_part_no,
                    OrderBundle.factory_id == factory_id,
                    OrderBundle.order_no == order_no
                ))
                .order_by(OrderBundle.size, OrderBundle.bundle_sequence)
            )
            return list(result.scalars().all())
        else:
            async with self.session_factory() as session:
                result = await session.execute(
                    select(OrderBundle)
                    .options(
                        selectinload(OrderBundle.order_part),
                        selectinload(OrderBundle.cutter),
                        selectinload(OrderBundle.sewer),
                        selectinload(OrderBundle.qc_user)
                    )
                    .where(and_(
                        OrderBundle.order_part_no == order_part_no,
                        OrderBundle.factory_id == factory_id,
                        OrderBundle.order_no == order_no
                    ))
                    .order_by(OrderBundle.size, OrderBundle.bundle_sequence)
                )
                return list(result.scalars().all())
    
    async def get_by_order_no_and_factory(self, order_no: str, factory_id: int, session: TypingOptional[AsyncSession] = None) -> List[OrderBundle]:
        if session is not None:
            result = await session.execute(
                select(OrderBundle)
                .options(
                    selectinload(OrderBundle.order_part),
                    selectinload(OrderBundle.cutter),
                    selectinload(OrderBundle.sewer),
                    selectinload(OrderBundle.qc_user)
                )
                .where(and_(OrderBundle.order_no == order_no, OrderBundle.factory_id == factory_id))
                .order_by(OrderBundle.order_part_no, OrderBundle.size, OrderBundle.bundle_sequence)
            )
            return list(result.scalars().all())
        else:
            async with self.session_factory() as session:
                result = await session.execute(
                    select(OrderBundle)
                    .options(
                        selectinload(OrderBundle.order_part),
                        selectinload(OrderBundle.cutter),
                        selectinload(OrderBundle.sewer),
                        selectinload(OrderBundle.qc_user)
                    )
                    .where(and_(OrderBundle.order_no == order_no, OrderBundle.factory_id == factory_id))
                    .order_by(OrderBundle.order_part_no, OrderBundle.size, OrderBundle.bundle_sequence)
                )
                return list(result.scalars().all())
    
    async def get_by_status(self, status: BundleStatus, factory_id: int) -> List[OrderBundle]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderBundle)
                .options(
                    selectinload(OrderBundle.order_part),
                    selectinload(OrderBundle.cutter),
                    selectinload(OrderBundle.sewer),
                    selectinload(OrderBundle.qc_user)
                )
                .where(and_(OrderBundle.status == status, OrderBundle.factory_id == factory_id))
                .order_by(OrderBundle.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_by_size(self, size: str, factory_id: int) -> List[OrderBundle]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderBundle)
                .options(
                    selectinload(OrderBundle.order_part),
                    selectinload(OrderBundle.cutter),
                    selectinload(OrderBundle.sewer),
                    selectinload(OrderBundle.qc_user)
                )
                .where(and_(OrderBundle.size == size, OrderBundle.factory_id == factory_id))
                .order_by(OrderBundle.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_by_user(self, user_id: int, factory_id: int, user_type: str = "any") -> List[OrderBundle]:
        async with self.session_factory() as session:
            conditions = [OrderBundle.factory_id == factory_id]
            
            if user_type == "cutter":
                conditions.append(OrderBundle.cutter_user_id == user_id)
            elif user_type == "sewer":
                conditions.append(OrderBundle.sewer_user_id == user_id)
            elif user_type == "qc":
                conditions.append(OrderBundle.qc_user_id == user_id)
            else:  # any
                conditions.append(or_(
                    OrderBundle.cutter_user_id == user_id,
                    OrderBundle.sewer_user_id == user_id,
                    OrderBundle.qc_user_id == user_id
                ))
            
            result = await session.execute(
                select(OrderBundle)
                .options(
                    selectinload(OrderBundle.order_part),
                    selectinload(OrderBundle.cutter),
                    selectinload(OrderBundle.sewer),
                    selectinload(OrderBundle.qc_user)
                )
                .where(and_(*conditions))
                .order_by(OrderBundle.created_at.desc())
            )
            return list(result.scalars().all())
    
    async def get_all(self, factory_id: int, skip: int = 0, limit: int = 100) -> List[OrderBundle]:
        async with self.session_factory() as session:
            result = await session.execute(
                select(OrderBundle)
                .options(
                    selectinload(OrderBundle.order_part),
                    selectinload(OrderBundle.cutter),
                    selectinload(OrderBundle.sewer),
                    selectinload(OrderBundle.qc_user)
                )
                .where(OrderBundle.factory_id == factory_id)
                .order_by(OrderBundle.created_at.desc())
                .offset(skip)
                .limit(limit)
            )
            return list(result.scalars().all())
    
    async def update(self, order_bundle: OrderBundle) -> OrderBundle:
        async with self.session_factory() as session:
            session.add(order_bundle)
            await session.commit()
            await session.refresh(order_bundle)
            return order_bundle
    
    async def delete(self, order_bundle_id: int) -> bool:
        async with self.session_factory() as session:
            result = await session.execute(
                delete(OrderBundle).where(OrderBundle.id == order_bundle_id)
            )
            await session.commit()
            return result.rowcount > 0
    
    async def delete_by_order_part_no_and_factory(self, order_part_no: str, factory_id: int, order_no: str) -> int:
        async with self.session_factory() as session:
            result = await session.execute(
                delete(OrderBundle).where(and_(
                    OrderBundle.order_part_no == order_part_no,
                    OrderBundle.factory_id == factory_id,
                    OrderBundle.order_no == order_no
                ))
            )
            await session.commit()
            return result.rowcount
    
    async def delete_by_order_no_and_factory(self, order_no: str, factory_id: int) -> int:
        async with self.session_factory() as session:
            result = await session.execute(
                delete(OrderBundle).where(and_(OrderBundle.order_no == order_no, OrderBundle.factory_id == factory_id))
            )
            await session.commit()
            return result.rowcount
    
    async def bulk_create(self, order_bundles: List[OrderBundle], session: TypingOptional[AsyncSession] = None) -> List[OrderBundle]:
        if session is not None:
            session.add_all(order_bundles)
            await session.flush()
            for order_bundle in order_bundles:
                await session.refresh(order_bundle)
            return order_bundles
        else:
            async with self.session_factory() as session:
                session.add_all(order_bundles)
                await session.commit()
                for order_bundle in order_bundles:
                    await session.refresh(order_bundle)
                return order_bundles
    
    async def get_order_bundle_statistics(self, factory_id: int, order_no: Optional[str] = None, order_part_no: Optional[str] = None) -> dict:
        async with self.session_factory() as session:
            base_query = select(OrderBundle).where(OrderBundle.factory_id == factory_id)
            
            if order_no:
                base_query = base_query.where(OrderBundle.order_no == order_no)
            if order_part_no:
                base_query = base_query.where(OrderBundle.order_part_no == order_part_no)
            
            # Total count
            total_result = await session.execute(
                select(func.count(OrderBundle.id)).select_from(base_query.subquery())
            )
            total_order_bundles = total_result.scalar()
            
            # Status breakdown
            status_result = await session.execute(
                select(OrderBundle.status, func.count(OrderBundle.id))
                .select_from(base_query.subquery())
                .group_by(OrderBundle.status)
            )
            status_breakdown = dict(status_result.all())
            
            # Quantity statistics
            quantity_result = await session.execute(
                select(
                    func.sum(OrderBundle.quantity),
                    func.sum(OrderBundle.completed_quantity),
                    func.sum(OrderBundle.defective_quantity),
                    func.sum(OrderBundle.rework_quantity)
                ).select_from(base_query.subquery())
            )
            quantity_stats = quantity_result.one()
            total_quantity = quantity_stats[0] or 0
            completed_quantity = quantity_stats[1] or 0
            defective_quantity = quantity_stats[2] or 0
            rework_quantity = quantity_stats[3] or 0
            
            return {
                "total_order_bundles": total_order_bundles,
                "status_breakdown": status_breakdown,
                "total_quantity": total_quantity,
                "completed_quantity": completed_quantity,
                "defective_quantity": defective_quantity,
                "rework_quantity": rework_quantity,
                "good_quantity": completed_quantity - defective_quantity,
                "completion_percentage": (completed_quantity / total_quantity * 100) if total_quantity > 0 else 0,
                "quality_rate": ((completed_quantity - defective_quantity) / completed_quantity * 100) if completed_quantity > 0 else 0,
                "planned_bundles": status_breakdown.get("planned", 0),
                "cutting_bundles": status_breakdown.get("cutting", 0),
                "cut_completed_bundles": status_breakdown.get("cut_completed", 0),
                "sewing_bundles": status_breakdown.get("sewing", 0),
                "sew_completed_bundles": status_breakdown.get("sew_completed", 0),
                "quality_check_bundles": status_breakdown.get("quality_check", 0),
                "completed_bundles": status_breakdown.get("completed", 0),
                "rework_bundles": status_breakdown.get("rework", 0),
                "on_hold_bundles": status_breakdown.get("on_hold", 0),
                "cancelled_bundles": status_breakdown.get("cancelled", 0)
            }