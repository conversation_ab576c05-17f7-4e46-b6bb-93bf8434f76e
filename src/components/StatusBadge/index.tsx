import { View, Text } from '@tarojs/components'
import './index.scss'

interface StatusBadgeProps {
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'ON_HOLD' | 'DELAYED' | 
          'pending' | 'in_progress' | 'completed' | 'skipped' |
          'PLANNED' | 'CUTTING' | 'SEWING' | 'QUALITY_CHECK'
  size?: 'small' | 'medium' | 'large'
  className?: string
}

const StatusBadge = ({ status, size = 'medium', className = '' }: StatusBadgeProps) => {
  const getStatusConfig = (status: string) => {
    const configs = {
      // Order statuses
      'PENDING': { label: '待处理', color: '#FFA500' },
      'IN_PROGRESS': { label: '进行中', color: '#1890FF' },
      'COMPLETED': { label: '已完成', color: '#52C41A' },
      'CANCELLED': { label: '已取消', color: '#FF4D4F' },
      'ON_HOLD': { label: '暂停', color: '#FAAD14' },
      'DELAYED': { label: '延期', color: '#F5222D' },
      
      // Craft statuses
      'pending': { label: '待开始', color: '#D9D9D9' },
      'in_progress': { label: '进行中', color: '#1890FF' },
      'completed': { label: '已完成', color: '#52C41A' },
      'skipped': { label: '已跳过', color: '#FAAD14' },
      
      // Part statuses
      'PLANNED': { label: '计划中', color: '#D9D9D9' },
      'CUTTING': { label: '裁剪中', color: '#1890FF' },
      'SEWING': { label: '缝制中', color: '#722ED1' },
      'QUALITY_CHECK': { label: '质检中', color: '#FA8C16' }
    }
    
    return configs[status] || { label: status, color: '#999999' }
  }

  const config = getStatusConfig(status)

  return (
    <View 
      className={`status-badge status-badge--${size} ${className}`}
      style={{ backgroundColor: config.color }}
    >
      <Text className="status-badge__text">{config.label}</Text>
    </View>
  )
}

export default StatusBadge
