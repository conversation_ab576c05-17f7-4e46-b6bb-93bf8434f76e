import { View, Text } from '@tarojs/components'
import './index.scss'

interface ProgressBarProps {
  percentage: number
  showText?: boolean
  size?: 'small' | 'medium' | 'large'
  color?: string
  backgroundColor?: string
  className?: string
  label?: string
}

const ProgressBar = ({ 
  percentage, 
  showText = true, 
  size = 'medium',
  color = '#1890FF',
  backgroundColor = '#f0f0f0',
  className = '',
  label
}: ProgressBarProps) => {
  const safePercentage = Math.max(0, Math.min(100, percentage))
  
  return (
    <View className={`progress-bar progress-bar--${size} ${className}`}>
      {label && (
        <View className="progress-bar__label">
          <Text className="progress-bar__label-text">{label}</Text>
          {showText && (
            <Text className="progress-bar__percentage">{safePercentage.toFixed(1)}%</Text>
          )}
        </View>
      )}
      <View 
        className="progress-bar__track"
        style={{ backgroundColor }}
      >
        <View 
          className="progress-bar__fill"
          style={{ 
            width: `${safePercentage}%`,
            backgroundColor: color
          }}
        />
      </View>
      {!label && showText && (
        <Text className="progress-bar__text">{safePercentage.toFixed(1)}%</Text>
      )}
    </View>
  )
}

export default ProgressBar
