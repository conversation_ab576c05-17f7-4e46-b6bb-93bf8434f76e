.progress-bar {
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;

  &--small {
    .progress-bar__track {
      height: 4px;
    }

    .progress-bar__text,
    .progress-bar__percentage {
      font-size: 10px;
    }
  }

  &--medium {
    .progress-bar__track {
      height: 6px;
    }

    .progress-bar__text,
    .progress-bar__percentage {
      font-size: 12px;
    }
  }

  &--large {
    .progress-bar__track {
      height: 8px;
    }

    .progress-bar__text,
    .progress-bar__percentage {
      font-size: 14px;
    }
  }

  &__label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    width: 100%;

    &-text {
      font-size: 12px;
      color: #333;
      font-weight: 500;
    }
  }

  &__track {
    flex: 1;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    min-width: 0;
    box-sizing: border-box;
  }

  &__fill {
    height: 100%;
    border-radius: inherit;
    transition: width 0.3s ease;
    min-width: 2px;
  }

  &__text,
  &__percentage {
    margin-left: 8px;
    color: #666;
    font-weight: 500;
    white-space: nowrap;
  }
}
