# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Taro-based cross-platform project for a MES (Manufacturing Execution System) Production Ticket System. It targets both WeChat Mini Program (微信小程序) and H5 web platforms using React and TypeScript.

## Development Commands

### Start Development Server
- `npm run dev:weapp` - WeChat Mini Program development
- `npm run dev:h5` - H5 web development
- `npm run dev:alipay` - Alipay Mini Program development
- `npm run dev:swan` - Baidu Smart Program development
- `npm run dev:tt` - ByteDance Mini Program development
- `npm run dev:qq` - QQ Mini Program development
- `npm run dev:jd` - JD Mini Program development

### Build for Production
- `npm run build:weapp` - Build WeChat Mini Program
- `npm run build:h5` - Build H5 web app
- `npm run build:alipay` - Build Alipay Mini Program
- `npm run build:swan` - Build Baidu Smart Program
- `npm run build:tt` - Build ByteDance Mini Program
- `npm run build:qq` - Build QQ Mini Program
- `npm run build:jd` - Build JD Mini Program

## Code Quality Commands
- `eslint` - ESLint is configured with Taro React preset
- `stylelint` - Style linting is available

## Project Structure

### Key Directories
- `src/` - Source code root
- `src/pages/` - Taro pages (similar to Next.js pages)
- `src/app.config.ts` - App-level configuration (page routes, window settings)
- `src/app.ts` - App entry point with lifecycle hooks
- `config/` - Build configuration (dev/prod environments)
- `types/` - TypeScript type definitions
- `dist/` - Build output directory

### Configuration Files
- `project.config.json` - WeChat Mini Program project configuration
- `tsconfig.json` - TypeScript configuration with `@/*` path mapping to `src/*`
- `babel.config.js` - Babel preset configured for Taro React with Vite compiler

## Architecture Notes

### Taro Framework
- Uses Taro 4.1.2 with React 18 and TypeScript
- Vite-based build system for faster development
- Cross-platform compilation targeting multiple mini-program platforms and H5

### Environment Variables
- `TARO_ENV` - Current build platform (weapp, h5, alipay, etc.)
- `TARO_APP_ID` - Mini Program App ID (configurable via env files)
- `NODE_ENV` - Development/production environment

### CSS Framework
- Sass/SCSS support
- PX to RPX transformation for mini-programs
- CSS Modules support (disabled by default)
- PostCSS with autoprefixer for H5

### Build Configuration
- Development config: `config/dev.ts`
- Production config: `config/prod.ts`
- Base config: `config/index.ts` with platform-specific settings

## Platform-Specific Considerations
- Mini-programs use RPX units for responsive design (750px design width)
- H5 builds support modern ES6+ features
- Different device ratio configurations for various screen sizes

## API Development
- Now, the backend is not ready, u can define the api at service dir and wrap Taro.request