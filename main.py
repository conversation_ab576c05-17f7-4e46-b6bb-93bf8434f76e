from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from config import settings
from src.infrastructure.containers import container
from src.presentation.api.v1 import api_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await container.db().engine.dispose()  # Ensure clean state
    yield
    # Shutdown
    await container.db().close()
    await container.redis().disconnect()


def create_app() -> FastAPI:
    """Create FastAPI application."""
    
    app = FastAPI(
        title=settings.api.project_name,
        version=settings.api.version,
        debug=settings.api.debug,
        lifespan=lifespan
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["http://localhost:5173", "http://127.0.0.1:5173", "http://localhost:10086"],  # Frontend origins
        allow_credentials=True,  
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )
    
    # Include API routes
    app.include_router(api_router, prefix=settings.api.v1_prefix)
    
    # Wire dependency injection
    container.wire(modules=[
        "src.presentation.api.v1.auth",
        "src.presentation.api.v1.auth_phone",
        "src.presentation.api.v1.users",
        "src.presentation.api.v1.factory_management",
        "src.presentation.api.v1.session",
        "src.presentation.api.v1.permissions",
        "src.presentation.api.v1.roles",
        "src.presentation.api.v1.user_management",
        "src.presentation.api.v1.skills",
        "src.presentation.api.v1.crafts",
        "src.presentation.api.v1.departments",
        "src.presentation.api.v1.orders",
        "src.presentation.api.v1.order_parts",
        "src.presentation.api.v1.order_bundles",
        "src.presentation.api.v1.order_crafts",
        "src.presentation.api.v1.craft_instances",
        "src.presentation.api.v1.available_registration",
        "src.presentation.api.v1.bills",
        "src.presentation.api.v1.statistics",
    ])
    
    return app


app = create_app()


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": f"Welcome to {settings.api.project_name} API v{settings.api.version}"}


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.api.debug
    )